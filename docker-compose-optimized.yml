# StageMinder Application Services (without Neo4j)
# This configuration connects to shared Neo4j service
# 
# Prerequisites: 
#   1. Start shared Neo4j first: docker-compose -f docker-compose.neo4j.yml up -d
#   2. Or use main compose: docker-compose -f docker-compose.main.yml up -d
#
# Usage:
#   docker-compose -f docker-compose-optimized.yml up -d

services:
  # Backend Service
  backend:
    build:
      context: ./Build/backend
      dockerfile: Dockerfile
    container_name: stageminder-backend
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      # Connect to shared Neo4j service
      - NEO4J_URI=bolt://stageminder-neo4j-shared:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=stageminder2024
      # AWS Configuration
      - AWS_ACCESS_KEY_ID=your_aws_access_key_here
      - AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
      - AWS_REGION=us-east-1
      - S3_BUCKET_NAME=your_s3_bucket_name_here
      - SERVER_ADDRESS=0.0.0.0
    ports:
      - "8080:8080"
    networks:
      - stageminder-shared-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Service
  frontend:
    build:
      context: ./Build/frontend
      dockerfile: Dockerfile
    container_name: stageminder-frontend
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://localhost/api
      - HOSTNAME=0.0.0.0
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - stageminder-shared-network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: stageminder-nginx
    ports:
      - "80:80"
    depends_on:
      - frontend
      - backend
    networks:
      - stageminder-shared-network
    restart: unless-stopped
    volumes:
      - ./Scripts/Configuration/nginx/nginx.conf:/etc/nginx/nginx.conf:ro

networks:
  stageminder-shared-network:
    external: true
    name: stageminder-shared-network
