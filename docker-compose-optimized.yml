# Admin Console Services (optimized for shared Neo4j)
# This configuration connects to shared Neo4j service
# 
# Prerequisites: 
#   1. Start shared Neo4j first: docker-compose -f docker-compose.neo4j.yml up -d
#   2. Or use main compose: docker-compose -f docker-compose.main.yml up -d
#
# Usage:
#   docker-compose -f docker-compose-optimized.yml up -d

services:
  # Admin Console Service
  admin-console:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: stageminder-admin-console
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      # Force Neo4j connection settings with multiple environment variables
      - SPRING_NEO4J_URI=bolt://stageminder-neo4j:7687
      - NEO4J_URI=bolt://stageminder-neo4j:7687
      - SPRING_NEO4J_AUTHENTICATION_USERNAME=neo4j
      - NEO4J_USERNAME=neo4j
      - SPRING_NEO4J_AUTHENTICATION_PASSWORD=stageminder2024
      - NEO4J_PASSWORD=stageminder2024
      # Connect to StageMinder backend if needed
      - STAGEMINDER_BACKEND_URL=http://stageminder-backend:8080
      # Disable system metrics to avoid ProcessorMetrics issues
      - MANAGEMENT_METRICS_BINDERS_SYSTEM_ENABLED=false
      # Override any hardcoded localhost settings
      - SPRING_DATA_NEO4J_URI=bolt://stageminder-neo4j:7687
    ports:
      - "8081:8081"
    networks:
      - stageminder-shared-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Admin Frontend (Nginx)
  admin-frontend:
    image: nginx:alpine
    container_name: admin-frontend
    ports:
      - "3001:80"
    volumes:
      - ./index.html:/usr/share/nginx/html/index.html:ro
      - ./style.css:/usr/share/nginx/html/style.css:ro
      - ./script.js:/usr/share/nginx/html/script.js:ro
      - ./logo.webp:/usr/share/nginx/html/logo.webp:ro
    networks:
      - stageminder-shared-network
    restart: unless-stopped
    depends_on:
      - admin-console

networks:
  stageminder-shared-network:
    external: true
    name: stageminder-shared-network
