#!/bin/bash

# StageMinder Local Development Docker Script

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

case "$1" in
    start)
        echo "Starting StageMinder services locally..."
        docker-compose -f docker-compose-local.yml up -d --build
        echo "Services started!"
        echo "Frontend: http://localhost:3000"
        echo "Backend API: http://localhost:8080"
        echo "With Nginx: http://localhost"
        echo "Neo4j Browser: http://localhost:7474"
        ;;
    stop)
        echo "Stopping StageMinder services..."
        docker-compose -f docker-compose-local.yml down
        ;;
    restart)
        echo "Restarting StageMinder services..."
        $0 stop
        sleep 3
        $0 start
        ;;
    rebuild)
        echo "Rebuilding and restarting services..."
        docker-compose -f docker-compose-local.yml down
        docker-compose -f docker-compose-local.yml build --no-cache
        docker-compose -f docker-compose-local.yml up -d
        ;;
    logs)
        docker-compose -f docker-compose-local.yml logs -f
        ;;
    status)
        docker-compose -f docker-compose-local.yml ps
        ;;
    clean)
        echo "Cleaning up Docker resources..."
        docker-compose -f docker-compose-local.yml down -v
        docker system prune -f
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|rebuild|logs|status|clean}"
        echo ""
        echo "Commands:"
        echo "  start   - Start all services"
        echo "  stop    - Stop all services"
        echo "  restart - Restart all services"
        echo "  rebuild - Rebuild images and restart"
        echo "  logs    - Show service logs"
        echo "  status  - Show service status"
        echo "  clean   - Clean up everything (including volumes)"
        exit 1
        ;;
esac 