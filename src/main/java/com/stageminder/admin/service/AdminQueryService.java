package com.stageminder.admin.service;

import org.neo4j.driver.Driver;
import org.neo4j.driver.Result;
import org.neo4j.driver.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AdminQueryService {

    @Autowired
    private Driver neo4jDriver;

    @Autowired(required = false)
    private RestTemplate restTemplate;

    @Value("${STAGEMINDER_BACKEND_URL:http://localhost:8080}")
    private String stageMinderBackendUrl;

    public List<Map<String, Object>> getAllUsers() {
        String query = """
            MATCH (u:User)
            RETURN u.firstName as firstName, u.lastName as lastName, u.email as email,
                   u.role as role, u.isEnabled as isEnabled, u.phoneNumber as phoneNumber,
                   u.createdAt as createdAt, u.lastLoginAt as lastLoginAt
            ORDER BY u.firstName
        """;
        return executeQuery(query);
    }

    public List<Map<String, Object>> searchUserByEmail(String email) {
        String query = """
            MATCH (u:User)
            WHERE u.email CONTAINS $email
            RETURN u.firstName as firstName, u.lastName as lastName, u.email as email,
                   u.role as role, u.isEnabled as isEnabled, u.phoneNumber as phoneNumber,
                   u.createdAt as createdAt, u.lastLoginAt as lastLoginAt,
                   u.registrationStatus as registrationStatus
            ORDER BY u.createdAt DESC
        """;
        try (Session session = neo4jDriver.session()) {
            return session.run(query, Map.of("email", email))
                    .stream()
                    .map(this::neoRecordToMap)
                    .collect(Collectors.toList());
        }
    }

    public Map<String, Object> getRegistrationAnalysis() {
        Map<String, Object> analysis = new LinkedHashMap<>();

        // Total registration attempts (including failed ones)
        String totalQuery = "MATCH (u:User) RETURN count(u) as totalUsers";
        analysis.put("totalUsers", singleCount(totalQuery));

        // Successful registrations
        String successQuery = "MATCH (u:User) WHERE u.isEnabled = true RETURN count(u) as successfulRegistrations";
        analysis.put("successfulRegistrations", singleCount(successQuery));

        // Failed registrations
        String failedQuery = "MATCH (u:User) WHERE u.isEnabled = false RETURN count(u) as failedRegistrations";
        analysis.put("failedRegistrations", singleCount(failedQuery));

        // Recent registration attempts (last 24 hours)
        String recentQuery = """
            MATCH (u:User)
            WHERE u.createdAt >= datetime() - duration('P1D')
            RETURN count(u) as recentAttempts
        """;
        analysis.put("recentAttempts", singleCount(recentQuery));

        return analysis;
    }

    public List<Map<String, Object>> getUsersWithLocation() {
        String query = """
            MATCH (u:User)-[:IS_LOCATED_AT]->(l:Location)
            RETURN (u.firstName + ' ' + u.lastName) AS fullName, 
                   u.email as email, 
                   u.role as role,
                   (l.city + ', ' + l.state + ', ' + l.country) AS location,
                   l.streetAddress as streetAddress,
                   l.zipCode as zipCode
            ORDER BY u.firstName
        """;
        return executeQuery(query);
    }

    public List<Map<String, Object>> getArtistUsers() {
        String query = """
            MATCH (u:User)
            WHERE u.role = 'ARTIST'
            RETURN u.firstName as firstName, u.lastName as lastName, 
                   u.email as email, u.isEnabled as isEnabled
            ORDER BY u.firstName
        """;
        return executeQuery(query);
    }

    public List<Map<String, Object>> getEnabledUsers() {
        String query = """
            MATCH (u:User)-[:IS_LOCATED_AT]->(l:Location)
            WHERE u.isEnabled = true
            RETURN (u.firstName + ' ' + u.lastName) AS fullName,
                   u.email as email,
                   u.phoneNumber as phoneNumber,
                   u.twoFaEnabled as twoFaEnabled,
                   (l.city + ', ' + l.country) AS location
            ORDER BY u.firstName
        """;
        return executeQuery(query);
    }

    public Map<String, Object> getUserStats() {
        String query = """
            MATCH (u:User)
            RETURN COUNT(u) AS totalUsers,
                   COUNT(CASE WHEN u.isEnabled = true THEN 1 END) AS enabledUsers,
                   COUNT(CASE WHEN u.twoFaEnabled = true THEN 1 END) AS twoFaUsers,
                   COUNT(CASE WHEN u.socialLoginUser = true THEN 1 END) AS socialLoginUsers
        """;
        List<Map<String, Object>> results = executeQuery(query);
        return results.isEmpty() ? new HashMap<>() : results.get(0);
    }

    public Map<String, Object> getDatabaseStatus() {
        String query = """
            CALL dbms.components() YIELD name, versions, edition
            RETURN name, versions[0] as version, edition
        """;
        try {
            List<Map<String, Object>> results = executeQuery(query);
            Map<String, Object> status = new HashMap<>();
            status.put("connected", true);
            status.put("timestamp", System.currentTimeMillis());

            if (!results.isEmpty()) {
                status.put("database", results.get(0));
            }

            String countQuery = "MATCH (n) RETURN count(n) as totalNodes";
            List<Map<String, Object>> countResults = executeQuery(countQuery);
            if (!countResults.isEmpty()) {
                status.put("totalNodes", countResults.get(0).get("totalNodes"));
            }

            return status;
        } catch (Exception e) {
            Map<String, Object> errorStatus = new HashMap<>();
            errorStatus.put("connected", false);
            errorStatus.put("error", e.getMessage());
            errorStatus.put("timestamp", System.currentTimeMillis());
            return errorStatus;
        }
    }

    public Map<String, Object> getMetricsSummary() {
        Map<String, Object> summary = new LinkedHashMap<>();
        summary.put("userStats", getUserStats());
        summary.put("profileStats", getProfileStats());
        summary.put("eventStats", getEventStats());
        summary.put("contractStats", getContractStats());
        summary.put("database", getDatabaseStatus());
        return summary;
    }

    public Map<String, Object> getProfileStats() {
        Map<String, Object> stats = new LinkedHashMap<>();
        stats.put("totalProfiles", singleCount("MATCH (p:Profile) RETURN count(p) as c"));
        stats.put("actProfiles", singleCount("MATCH (p:Profile {profileType: 'ACT_PROFILE'}) RETURN count(p) as c"));
        stats.put("venueProfiles", singleCount("MATCH (p:Profile {profileType: 'VENUE_PROFILE'}) RETURN count(p) as c"));
        stats.put("virtualActProfiles", singleCount("MATCH (p:Profile {profileType: 'VIRTUAL_ACT_PROFILE'}) RETURN count(p) as c"));
        stats.put("virtualVenueProfiles", singleCount("MATCH (p:Profile {profileType: 'VIRTUAL_VENUE_PROFILE'}) RETURN count(p) as c"));
        stats.put("publishedProfiles", singleCount("MATCH (p:Profile {status: 'STATUS_PUBLISHED'}) RETURN count(p) as c"));
        stats.put("createdProfiles", singleCount("MATCH (p:Profile {status: 'STATUS_CREATED'}) RETURN count(p) as c"));
        stats.put("deletedProfiles", singleCount("MATCH (p:Profile {status: 'STATUS_DELETED'}) RETURN count(p) as c"));
        return stats;
    }

    public Map<String, Object> getEventStats() {
        Map<String, Object> stats = new LinkedHashMap<>();
        stats.put("totalEvents", singleCount("MATCH (e:Event) RETURN count(e) as c"));
        stats.put("publishedEvents", singleCount("MATCH (e:Event {status: 'STATUS_PUBLISHED'}) RETURN count(e) as c"));

        String q = "MATCH (e:Event) RETURN e.status as status, count(e) as count";
        List<Map<String, Object>> rows = executeQuery(q);
        Map<String, Object> byStatus = new LinkedHashMap<>();
        for (Map<String, Object> row : rows) {
            Object status = row.get("status");
            Object count = row.get("count");
            if (status != null) {
                byStatus.put(status.toString(), count);
            }
        }
        stats.put("byStatus", byStatus);
        return stats;
    }

    public Map<String, Object> getContractStats() {
        Map<String, Object> stats = new LinkedHashMap<>();
        stats.put("totalContracts", singleCount("MATCH (c:Contract) RETURN count(c) as c"));
        Map<String, Object> byState = new LinkedHashMap<>();
        String q = "MATCH (c:Contract) RETURN c.contractState as state, count(c) as count";
        List<Map<String, Object>> rows = executeQuery(q);
        for (Map<String, Object> row : rows) {
            Object state = row.get("state");
            Object count = row.get("count");
            if (state != null) {
                byState.put(state.toString(), count);
            }
        }
        stats.put("byState", byState);
        return stats;
    }

    public List<Map<String, Object>> getContractsTimeSeries30d() {
        String q = """
            MATCH (c:Contract)
            WHERE c.timeStamp >= datetime() - duration('P30D')
            RETURN date(c.timeStamp) AS date, count(c) AS count
            ORDER BY date
        """;
        try (Session session = neo4jDriver.session()) {
            Result result = session.run(q);
            List<Map<String, Object>> list = new ArrayList<>();
            while (result.hasNext()) {
                org.neo4j.driver.Record r = result.next();
                LocalDate d = r.get("date").asLocalDate();
                long c = r.get("count").asLong();
                Map<String, Object> m = new LinkedHashMap<>();
                m.put("date", d != null ? d.toString() : null);
                m.put("count", c);
                list.add(m);
            }
            return list;
        }
    }

    public List<Map<String, Object>> getEventsTimeSeries30d() {
        String q = """
            MATCH (ct:Contract)-[:HAS_SCHEDULE]->(s:ScheduleTime)
            WHERE s.startDate >= datetime() AND s.startDate < datetime() + duration('P30D')
            RETURN date(s.startDate) AS date, count(*) AS count
            ORDER BY date
        """;
        try (Session session = neo4jDriver.session()) {
            Result result = session.run(q);
            List<Map<String, Object>> list = new ArrayList<>();
            while (result.hasNext()) {
                org.neo4j.driver.Record r = result.next();
                LocalDate d = r.get("date").asLocalDate();
                long c = r.get("count").asLong();
                Map<String, Object> m = new LinkedHashMap<>();
                m.put("date", d != null ? d.toString() : null);
                m.put("count", c);
                list.add(m);
            }
            return list;
        }
    }

    public List<Map<String, Object>> getUsersByCountry() {
        String q = """
            MATCH (u:User)-[:IS_LOCATED_AT]->(l:Location)
            WITH coalesce(l.country, 'Unknown') AS country, count(u) AS users
            RETURN country, users
            ORDER BY users DESC
            LIMIT 20
        """;
        return executeQuery(q);
    }

    public Map<String, Object> getServiceHealth() {
        Map<String, Object> health = new LinkedHashMap<>();
        // Admin Console
        health.put("adminConsole", Map.of(
                "status", "UP",
                "timestamp", System.currentTimeMillis()
        ));

        // Database
        health.put("database", getDatabaseStatus());

        // StageMinder Backend
        Map<String, Object> backend = new LinkedHashMap<>();
        try {
            if (restTemplate != null && stageMinderBackendUrl != null && !stageMinderBackendUrl.isBlank()) {
                String url = stageMinderBackendUrl.endsWith("/") ? stageMinderBackendUrl + "actuator/health" : stageMinderBackendUrl + "/actuator/health";
                @SuppressWarnings("unchecked")
                ResponseEntity<Map<String, Object>> resp = restTemplate.getForEntity(url, (Class<Map<String, Object>>)(Class<?>)Map.class);
                backend.put("reachable", true);
                backend.put("statusCode", resp.getStatusCode().value());
                backend.put("body", resp.getBody());
            } else {
                backend.put("reachable", false);
                backend.put("error", "RestTemplate or backend URL not configured");
            }
        } catch (Exception ex) {
            backend.put("reachable", false);
            backend.put("error", ex.getMessage());
        }
        health.put("backend", backend);

        return health;
    }

    public List<Map<String, Object>> executeCustomQuery(String cypher) {
        String upperCypher = cypher.toUpperCase().trim();
        if (upperCypher.startsWith("DELETE") ||
                upperCypher.startsWith("REMOVE") ||
                upperCypher.startsWith("DROP") ||
                upperCypher.startsWith("CREATE") ||
                upperCypher.startsWith("MERGE") ||
                upperCypher.startsWith("SET")) {
            throw new IllegalArgumentException("Destructive operations are not allowed in admin console");
        }

        return executeQuery(cypher);
    }

    private long singleCount(String cypher) {
        try (Session session = neo4jDriver.session()) {
            org.neo4j.driver.Record r = session.run(cypher).single();
            // Check which field is available in the result
            if (r.containsKey("count")) {
                return r.get("count").asLong();
            } else {
                return r.get("c").asLong();
            }
        }
    }

    private List<Map<String, Object>> executeQuery(String cypher) {
        try (Session session = neo4jDriver.session()) {
            Result result = session.run(cypher);
            return result.stream()
                    .map(this::neoRecordToMap)
                    .collect(Collectors.toList());
        }
    }

    public int insertSampleActs() {
        String query = """
            UNWIND [
                {name: 'The Neon Waves', genre: 'Electronic', location: 'Berlin', description: 'Cutting-edge electronic music collective'},
                {name: 'Sunset Jazz Quartet', genre: 'Jazz', location: 'New York', description: 'Smooth jazz with modern twist'},
                {name: 'Mountain Echo', genre: 'Folk', location: 'Denver', description: 'Acoustic folk band with mountain influences'},
                {name: 'Urban Pulse', genre: 'Hip-Hop', location: 'Los Angeles', description: 'Contemporary hip-hop artists'},
                {name: 'Crystal Harmony', genre: 'Pop', location: 'London', description: 'Melodic pop with ethereal vocals'},
                {name: 'Thunder Road', genre: 'Rock', location: 'Nashville', description: 'Classic rock with southern flavor'},
                {name: 'Digital Dreams', genre: 'Synthwave', location: 'Tokyo', description: 'Retro-futuristic electronic music'},
                {name: 'Velvet Strings', genre: 'Classical', location: 'Vienna', description: 'Modern classical ensemble'},
                {name: 'Midnight Blues', genre: 'Blues', location: 'Chicago', description: 'Traditional blues with contemporary edge'},
                {name: 'Cosmic Drift', genre: 'Ambient', location: 'Amsterdam', description: 'Atmospheric ambient soundscapes'}
            ] AS act
            CREATE (a:Profile {
                profileType: 'ACT_PROFILE',
                profileName: act.name,
                genre: act.genre,
                location: act.location,
                description: act.description,
                status: 'STATUS_PUBLISHED',
                createdAt: datetime(),
                updatedAt: datetime(),
                profileId: randomUUID()
            })
            RETURN count(a) as inserted
        """;
        try (Session session = neo4jDriver.session()) {
            org.neo4j.driver.Record result = session.run(query).single();
            return (int) result.get("inserted").asLong();
        }
    }

    public Map<String, Object> getActsVenuesStats() {
        Map<String, Object> stats = new LinkedHashMap<>();
        
        // Get act profiles count
        String actQuery = "MATCH (p:Profile) WHERE p.profileType = 'ACT_PROFILE' RETURN count(p) as count";
        long actCount = singleCount(actQuery);
        
        // Get venue profiles count
        String venueQuery = "MATCH (p:Profile) WHERE p.profileType = 'VENUE_PROFILE' RETURN count(p) as count";
        long venueCount = singleCount(venueQuery);
        
        // Get virtual act profiles count
        String virtualActQuery = "MATCH (p:Profile) WHERE p.profileType = 'VIRTUAL_ACT_PROFILE' RETURN count(p) as count";
        long virtualActCount = singleCount(virtualActQuery);
        
        // Get virtual venue profiles count
        String virtualVenueQuery = "MATCH (p:Profile) WHERE p.profileType = 'VIRTUAL_VENUE_PROFILE' RETURN count(p) as count";
        long virtualVenueCount = singleCount(virtualVenueQuery);
        
        // Get published counts
        String publishedActQuery = "MATCH (p:Profile) WHERE p.profileType = 'ACT_PROFILE' AND p.status = 'STATUS_PUBLISHED' RETURN count(p) as count";
        long publishedActCount = singleCount(publishedActQuery);
        
        String publishedVenueQuery = "MATCH (p:Profile) WHERE p.profileType = 'VENUE_PROFILE' AND p.status = 'STATUS_PUBLISHED' RETURN count(p) as count";
        long publishedVenueCount = singleCount(publishedVenueQuery);
        
        stats.put("totalActs", actCount);
        stats.put("totalVenues", venueCount);
        stats.put("virtualActs", virtualActCount);
        stats.put("virtualVenues", virtualVenueCount);
        stats.put("publishedActs", publishedActCount);
        stats.put("publishedVenues", publishedVenueCount);
        stats.put("totalProfiles", actCount + venueCount + virtualActCount + virtualVenueCount);
        
        return stats;
    }

    public List<Map<String, Object>> getAllActs() {
        String query = """
            MATCH (p:Profile)
            WHERE p.profileType = 'ACT_PROFILE' OR p.profileType = 'VIRTUAL_ACT_PROFILE'
            OPTIONAL MATCH (p)-[:IS_LOCATED_AT]->(loc:Location)
            OPTIONAL MATCH (p)-[:HAS_INFO]->(info:ProfileInfo)
            OPTIONAL MATCH (p)-[:HAS_SKILLS]->(skills:ActSkills)-[:HAS_ENTERTAINMENT_TYPE]->(et:EntertainmentType)
            RETURN p.profileId as profileId,
                   p.profileName as name,
                   et.name as genre,
                   CASE
                     WHEN loc IS NOT NULL THEN loc.city + ', ' + loc.state + ', ' + loc.country
                     ELSE 'No location'
                   END as location,
                   COALESCE(info.bio, 'No description available') as description,
                   COALESCE(p.status, 'STATUS_CREATED') as status,
                   p.profileType as type,
                   p.numberOfFollowers as followers,
                   p.gigsPerMonth as gigsPerMonth,
                   p.averageGigsPrice as averagePrice,
                   'N/A' as createdAt
            ORDER BY p.profileName
        """;
        return executeQuery(query);
    }

    public List<Map<String, Object>> getAllVenues() {
        String query = """
            MATCH (p:Profile)
            WHERE p.profileType = 'VENUE_PROFILE' OR p.profileType = 'VIRTUAL_VENUE_PROFILE'
            RETURN p.profileName as name, p.location as location, 
                   p.description as description, p.status as status, p.profileType as type,
                   p.createdAt as createdAt, p.updatedAt as updatedAt
            ORDER BY p.createdAt DESC
        """;
        return executeQuery(query);
    }

    private Map<String, Object> neoRecordToMap(org.neo4j.driver.Record record) {
        Map<String, Object> map = new HashMap<>();
        record.keys().forEach(key -> {
            Object value = record.get(key).asObject();
            // Normalize java.time types to string for JSON safety
            if (value instanceof LocalDate) {
                map.put(key, value.toString());
            } else {
                map.put(key, value);
            }
        });
        return map;
    }
}