package com.stageminder.admin.controller;

import com.stageminder.admin.service.AdminQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/admin")
@CrossOrigin(origins = "*")
public class AdminController {

    @Autowired
    private AdminQueryService adminQueryService;

    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        return ResponseEntity.ok(Map.of(
            "status", "OK", 
            "service", "StageMinder Admin Console",
            "timestamp", String.valueOf(System.currentTimeMillis())
        ));
    }

    @GetMapping("/users")
    public ResponseEntity<List<Map<String, Object>>> getAllUsers() {
        try {
            List<Map<String, Object>> users = adminQueryService.getAllUsers();
            return ResponseEntity.ok(users);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/users/with-location")
    public ResponseEntity<List<Map<String, Object>>> getUsersWithLocation() {
        try {
            List<Map<String, Object>> users = adminQueryService.getUsersWithLocation();
            return ResponseEntity.ok(users);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/users/artists")
    public ResponseEntity<List<Map<String, Object>>> getArtistUsers() {
        try {
            List<Map<String, Object>> artists = adminQueryService.getArtistUsers();
            return ResponseEntity.ok(artists);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/users/enabled")
    public ResponseEntity<List<Map<String, Object>>> getEnabledUsers() {
        try {
            List<Map<String, Object>> users = adminQueryService.getEnabledUsers();
            return ResponseEntity.ok(users);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/users/stats")
    public ResponseEntity<Map<String, Object>> getUserStats() {
        try {
            Map<String, Object> stats = adminQueryService.getUserStats();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/users/search")
    public ResponseEntity<List<Map<String, Object>>> searchUserByEmail(@RequestParam String email) {
        try {
            List<Map<String, Object>> users = adminQueryService.searchUserByEmail(email);
            return ResponseEntity.ok(users);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/users/registration-analysis")
    public ResponseEntity<Map<String, Object>> getRegistrationAnalysis() {
        try {
            Map<String, Object> analysis = adminQueryService.getRegistrationAnalysis();
            return ResponseEntity.ok(analysis);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/metrics/summary")
    public ResponseEntity<Map<String, Object>> getMetricsSummary() {
        try {
            return ResponseEntity.ok(adminQueryService.getMetricsSummary());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/metrics/profiles")
    public ResponseEntity<Map<String, Object>> getProfileStats() {
        try {
            return ResponseEntity.ok(adminQueryService.getProfileStats());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/metrics/events")
    public ResponseEntity<Map<String, Object>> getEventStats() {
        try {
            return ResponseEntity.ok(adminQueryService.getEventStats());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/metrics/venues-acts")
    public ResponseEntity<Map<String, Object>> getActsVenuesSummary() {
        try {
            return ResponseEntity.ok(adminQueryService.getProfileStats());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/metrics/contracts")
    public ResponseEntity<Map<String, Object>> getContractStats() {
        try {
            return ResponseEntity.ok(adminQueryService.getContractStats());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/metrics/contracts/series30d")
    public ResponseEntity<List<Map<String, Object>>> getContractsSeries30d() {
        try {
            return ResponseEntity.ok(adminQueryService.getContractsTimeSeries30d());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/metrics/events/series30d")
    public ResponseEntity<List<Map<String, Object>>> getEventsSeries30d() {
        try {
            return ResponseEntity.ok(adminQueryService.getEventsTimeSeries30d());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/metrics/users/by-country")
    public ResponseEntity<List<Map<String, Object>>> getUsersByCountry() {
        try {
            return ResponseEntity.ok(adminQueryService.getUsersByCountry());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/system/health")
    public ResponseEntity<Map<String, Object>> getSystemHealth() {
        try {
            return ResponseEntity.ok(adminQueryService.getServiceHealth());
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping("/query")
    public ResponseEntity<List<Map<String, Object>>> executeCustomQuery(@RequestBody Map<String, String> request) {
        try {
            String cypher = request.get("query");
            if (cypher == null || cypher.trim().isEmpty()) {
                return ResponseEntity.badRequest().build();
            }
            List<Map<String, Object>> results = adminQueryService.executeCustomQuery(cypher);
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/database/status")
    public ResponseEntity<Map<String, Object>> getDatabaseStatus() {
        try {
            Map<String, Object> status = adminQueryService.getDatabaseStatus();
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping("/acts/insert-sample")
    public ResponseEntity<Map<String, Object>> insertSampleActs() {
        try {
            int inserted = adminQueryService.insertSampleActs();
            return ResponseEntity.ok(Map.of(
                "message", "Sample acts inserted successfully",
                "inserted", inserted,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/acts-venues/stats")
    public ResponseEntity<Map<String, Object>> getActsVenuesStats() {
        try {
            Map<String, Object> stats = adminQueryService.getActsVenuesStats();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/acts")
    public ResponseEntity<List<Map<String, Object>>> getAllActs() {
        try {
            List<Map<String, Object>> acts = adminQueryService.getAllActs();
            return ResponseEntity.ok(acts);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/venues")
    public ResponseEntity<List<Map<String, Object>>> getAllVenues() {
        try {
            List<Map<String, Object>> venues = adminQueryService.getAllVenues();
            return ResponseEntity.ok(venues);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }
}