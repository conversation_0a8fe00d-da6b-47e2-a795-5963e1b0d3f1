package com.stageminder.admin.config;

import org.neo4j.driver.Driver;
import org.neo4j.driver.GraphDatabase;
import org.neo4j.driver.AuthTokens;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
public class Neo4jConfig {

    @Value("${NEO4J_URI:bolt://localhost:7687}")
    private String neo4jUri;

    @Value("${NEO4J_USERNAME:neo4j}")
    private String neo4jUsername;

    @Value("${NEO4J_PASSWORD:stageminder2024}")
    private String neo4jPassword;

    @Bean
    public Driver neo4jDriver() {
        return GraphDatabase.driver(neo4jUri, AuthTokens.basic(neo4jUsername, neo4jPassword));
    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}