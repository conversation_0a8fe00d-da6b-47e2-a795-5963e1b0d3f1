server:
  port: 8081
  servlet:
    context-path: /
    
spring:
  application:
    name: stageminder-admin-console
  
  # Neo4j Configuration
  neo4j:
    uri: ${SPRING_NEO4J_URI:bolt://stageminder-neo4j:7687}
    authentication:
      username: ${SPRING_NEO4J_AUTHENTICATION_USERNAME:neo4j}
      password: ${SPRING_NEO4J_AUTHENTICATION_PASSWORD:stageminder2024}
    
  # Web Configuration
  web:
    cors:
      allowed-origins: "*"
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: "*"
      allow-credentials: true

# Logging Configuration
logging:
  level:
    com.stageminder.admin: DEBUG
    org.neo4j: INFO
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Management and Actuator
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: always
  info:
    env:
      enabled: true

stageminder:
  backend-url: ${STAGEMINDER_BACKEND_URL:http://localhost:8080}

# Application Info
info:
  app:
    name: StageMinder Admin Console
    description: Admin Console Microservice for StageMinder
    version: 1.0.0
    developer: StageMinder Team