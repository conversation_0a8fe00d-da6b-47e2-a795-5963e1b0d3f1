# Stage Minder
## Introduction

**StageMinder** is a full-stack web application designed to provide a robust and interactive portal where Acts (e.g., performers, artists) and Venues (e.g., event spaces, theaters) can seamlessly connect and collaborate. The application features a **Spring Boot** backend for handling business logic and REST API services, a **Next.js** frontend for delivering a dynamic and responsive user interface, and **Nginx** as a reverse proxy and web server to efficiently route traffic. The project is structured to support streamlined development, building, and deployment processes through automation scripts, ensuring scalability and maintainability. StageMinder leverages a Neo4j graph database for efficient data relationships, AWS S3 for storing images and PDFs, and is deployed on an AWS EC2 instance for reliable performance.

The followings in this folder provide a quick start.
1. User Manual for using the Stage Minder Website.pdf - It explains how to use the Stage Minder
2. freshStageMinder.sh - this script apply configuration, build, deploy and run the Stage Minder.
3. runStageMinder.sh - this script will run the Stage Minder after it's build once

The following is the high level architecture 

![Stage  Minder System](images/StageMinderSystem.PNG)

## System Requirements

StageMinder requires an **AWS EC2 t4g.xlarge** instance running **Ubuntu 22.04 LTS** (Jammy Jellyfish) for deployment. The t4g.xlarge instance is part of the AWS Graviton2-based T4g family, optimized for cost-effective performance in general-purpose workloads.

- **EC2 Instance Specifications**:
  - **Instance Type**: t4g.xlarge
  - **Operating System**: Ubuntu 22.04 LTS
  - **vCPUs**: 4
  - **Memory**: 16 GiB
  - **Processor**: AWS Graviton2 (Arm-based)
  - **Network Performance**: Up to 5 Gbps
  - **Storage**: EBS-only (configure appropriate EBS volume size based on your needs)
  - **Use Case**: Suitable for web servers, application servers, and moderate workloads requiring balanced compute, memory, and network resources.
- **Access**: Connect to the EC2 instance using **SSH** on **port 22** with the provided PuTTY key (.ppk file) for secure remote access. Ensure the security group allows inbound traffic on port 22 from your IP address or a trusted range.
- **Note**: Ensure the instance is configured with sufficient EBS storage for application artifacts, logs, and dependencies, and that security groups allow traffic on ports 22 (SSH), 443 (Nginx)

- **Neo4j Database**:
  - **Description**: Neo4j is a graph database used by StageMinder to store and query relational data efficiently. It is ideal for applications requiring complex relationships and fast querying of connected data.
  - **Version**: Recommended to use the latest stable version compatible with Ubuntu 22.04 (e.g., Neo4j 5.x Community or Enterprise Edition).
  - **Configuration**: Managed via files in `Scripts/Configuration/neo4j/`. Ensure proper setup of connection details in `Build/backend/src/main/resources/application.properties`.
  - **Database Deletion (Caution)**: To clear the entire Neo4j database, you can use the Cypher query:
    ```cypher
    MATCH (n) DETACH DELETE n
    ```
    **Warning**: This command deletes all nodes and relationships in the database and is **irreversible**. Always create a backup using scripts in `Scripts/DbScripts/` (e.g., `backup_db.sh`) before executing this command to avoid permanent data loss.

Images and PDFs are stored in **AWS S3**, requiring proper IAM permissions and bucket configuration for access by the StageMinder backend.

## Folder Structure
Each of the sub folders has additional ReadMe files that provides more details.

The top-level directory is organized as follows:

- **`Build/`**: Contains source code for the StageMinder application.
  - **`frontend/`**: A Next.js project for the frontend, with the following typical structure:
    - **`messages/`**: Contains translations for English, French, etc.
    - **`public/`**: Static assets like images or icons (note: application images and PDFs are stored in AWS S3).
    - **`src/`**: All the sources are inside this folder..
      - **`app/`**  Next.js 13+ app directory
      - **`assets/`**  Static assets (images, fonts)
      - **`component/`**  Reusable React components
      - **`config/`**  Configuration files
      - **`context/`**  React context providers
      - **`ui/`**  UI components and layouts
      - **`utils/`**  Utility functions
    - **`next.config.mjs`**: Configuration file for Next.js.
    - **`package.json`**: Node.js dependencies and scripts for building/running the frontend.
    - **`.env`**: Environment variables for local development (e.g., API endpoints, S3 bucket details).
  - **`backend/`**: A Spring Boot project for the backend, with the following typical structure:
    - **`src/main/java/`**: Java source files, including controllers, services, and models (e.g., `com.example.stageminder` package).
    - **`src/main/resources/`**: Configuration files (e.g., `application.properties` or `application.yml`) and static resources.
    - **`pom.xml`**: Maven configuration file for dependencies and build settings.
    - **`config/`**: Run time / active configurations that overwrites the default config.
    - **`json-files`**: Json files that get loaded into DB at the first time it runs.
    - **`images/`**: icons for the application.
    - **`pdf-files/`**: pdf files.
- **`Run/`**: Stores deployed artifacts (e.g., compiled Next.js frontend and Spring Boot JAR file) generated by the build process.
- **`Scripts/`**: Contains scripts for managing the StageMinder system.
  - **`DbScripts/`**: Database-related scripts, such as backup and maintenance scripts for Neo4j.
  - **`ServiceCtrl/`**: Scripts for controlling services, such as starting and stopping services.
  - **`Configuration/`**: Configuration files for the StageMinder system.
    - **`applied/`**: Stores copies of configurations applied during the last run, updated by `runStageMinder.sh` and `freshStageMinder.sh`.
    - **`neo4j/`**: Configuration files for the Neo4j database.
    - **`ngnix/`**: Nginx server configuration files.
      - **`SSL/`**: SSL certificates and related configuration scripts for Nginx.
    - **`server/`**: Frontend and backend configuration files for StageMinder.
    - **`other/`**: Alternative or inactive configurations not currently in use.

## Nginx Configuration

Nginx serves as the reverse proxy, running on port **443** (HTTPS) with SSL configurations from `Scripts/Configuration/ngnix/SSL/`. It routes incoming requests as follows:
- URLs matching `/api/`, `/ws/`, or `/oauth2/` are proxied to the **backend** running on port **8080**.
- All other URLs are proxied to the **frontend** running on port **3000**.

Ensure Nginx is properly configured in `Scripts/Configuration/ngnix/` to handle these routes and that SSL certificates are valid.

## Usage

### 1. Database Scripts (`Scripts/DbScripts/`)
- Use these scripts for Neo4j database operations, such as backups or maintenance.
- Example: Run `bash backup_db.sh` to create a backup of the StageMinder database.
- **Caution**: Before performing destructive operations like `MATCH (n) DETACH DELETE n`, ensure a backup is created to prevent data loss.

### 2. Service Control Scripts (`Scripts/ServiceCtrl/`)
- Use these scripts to start, stop, or manage StageMinder services.
- Example: Run `bash start_service.sh` to start a specific service.

### 3. Building and Deploying the StageMinder Server
- Use the `freshStageMinder.sh` script for a fresh setup and deployment. This script:
  1. Sets configurations from `Scripts/Configuration/`.
  2. Builds the **Next.js frontend** (from `Build/frontend/`) using `npm run build` and the **Spring Boot backend** (from `Build/backend/`) using `mvn package`.
  3. Deploys the resulting artifacts (e.g., Next.js build output and Spring Boot JAR) to the `Run/` directory.
  4. Executes the `runStageMinder.sh` script to start the server.
- **Important**: Before running `freshStageMinder.sh`, ensure no frontend processes are running on port 3000 to avoid conflicts.
- Example: Run `bash Scripts/freshStageMinder.sh` to build, deploy, and start the StageMinder server.

### 4. Running the StageMinder Server
- Use the `runStageMinder.sh` script to start the StageMinder server (backend on port 8080, then frontend on port 3000). This script also copies applied configurations to `Scripts/Configuration/applied/`.
- **Important**: Ensure no frontend processes are running on port 3000 before executing this script to avoid conflicts.
- Example: Run `bash Scripts/runStageMinder.sh` to start the StageMinder server.

### 5. Configuration Management (`Scripts/Configuration/`)
- Modify configuration files in subdirectories (`neo4j/`, `ngnix/`, `server/`) as needed.
- The `applied/` folder is automatically updated by `runStageMinder.sh` and `freshStageMinder.sh` to reflect configurations used in the last run.
- SSL certificates and configurations are managed in `ngnix/SSL/`.
- Inactive or alternative configurations can be stored in `other/`.
- Configure S3 bucket access (e.g., via AWS SDK) in `Build/backend/src/main/resources/application.properties` for image and PDF retrieval.
- Configure Neo4j connection details in `Build/backend/src/main/resources/application.properties` and `Scripts/Configuration/neo4j/`.

**Note**: Before running any `.sh` script (e.g., `freshStageMinder.sh`, `runStageMinder.sh`), ensure it has executable permissions:
```bash
chmod +x script_name.sh
