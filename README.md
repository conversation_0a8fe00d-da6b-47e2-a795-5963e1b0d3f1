# StageMinder Admin Console

A standalone microservice for administering the StageMinder Neo4j database with a modern web interface.

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │  Admin Service  │    │     Neo4j       │
│   (HTML/CSS/JS) │───▶│  (Spring Boot)  │───▶│   Database      │
│   Port: File    │    │   Port: 8081    │    │   Port: 7474    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Java 17 or higher
- Maven 3.6 or higher
- Neo4j Database running on localhost:7474

### Method 1: Using Startup Scripts

**Windows:**
```bash
./start-admin.bat
```

**Linux/Mac:**
```bash
chmod +x start-admin.sh
./start-admin.sh
```

### Method 2: Manual Start

1. **Start the backend service:**
```bash
mvn clean spring-boot:run
```

2. **Open the frontend:**
   - Open `index.html` in your web browser
   - Or serve it via a simple HTTP server

### Method 3: Docker Compose

```bash
# Start the entire stack
docker-compose up -d

# View logs
docker-compose logs -f admin-console

# Stop the stack
docker-compose down
```

## 📋 Available Endpoints

### Backend API (Port 8081)

- **Health Check:** `GET /api/admin/health`
- **All Users:** `GET /api/admin/users`
- **Users with Location:** `GET /api/admin/users/with-location`
- **Artist Users:** `GET /api/admin/users/artists`
- **Enabled Users:** `GET /api/admin/users/enabled`
- **User Statistics:** `GET /api/admin/users/stats`
- **Database Status:** `GET /api/admin/database/status`
- **Custom Query:** `POST /api/admin/query`

### Frontend Features

- 🔍 **Real-time Query Execution**
- 📊 **User Statistics Dashboard**
- 🗺️ **Location-based User Queries**
- 🎨 **Modern Responsive UI**
- ⚡ **Fast API-based Architecture**

## 🔧 Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `SPRING_PROFILES_ACTIVE` | `dev` | Spring profile |
| `NEO4J_URI` | `bolt://localhost:7687` | Neo4j connection URI |
| `NEO4J_USERNAME` | `neo4j` | Neo4j username |
| `NEO4J_PASSWORD` | `stageminder2024` | Neo4j password |

### Application Properties

Edit `src/main/resources/application.yml`:

```yaml
server:
  port: 8081

spring:
  neo4j:
    uri: bolt://localhost:7687
    authentication:
      username: neo4j
      password: stageminder2024
```

## 🗄️ Database Setup

### 1. Start Neo4j Database

```bash
# Using Docker (recommended)
docker run -d \
  --name neo4j \
  -p 7474:7474 -p 7687:7687 \
  -e NEO4J_AUTH=neo4j/stageminder2024 \
  neo4j:5.15-community

# Or use the existing StageMinder Neo4j instance
cd ../StageMinder
docker-compose up neo4j -d
```

### 2. Insert Test Data

Access Neo4j Browser at http://localhost:7474 and run:

```cypher
// Create Users and Locations
CREATE 
(u1:User {
    firstName: "John",
    lastName: "Smith", 
    email: "<EMAIL>",
    password: "hashedPassword123",
    role: "ARTIST",
    isEnabled: true,
    twoFaEnabled: false,
    phoneNumber: "******-0101",
    socialLoginUser: false
}),
(l1:Location {
    country: "United States",
    city: "New York",
    state: "New York", 
    streetAddress: "123 Broadway Ave",
    zipCode: "10001",
    canTravelLongDistance: true,
    latitude: 40.7128,
    longitude: -74.0060
}),
(u1)-[:IS_LOCATED_AT]->(l1);

// Repeat for more users...
```

## 🛠️ Development

### Project Structure

```
adminconsole/
├── src/
│   └── main/
│       ├── java/com/stageminder/admin/
│       │   ├── AdminConsoleApplication.java
│       │   ├── config/
│       │   │   ├── CorsConfig.java
│       │   │   └── Neo4jConfig.java
│       │   ├── controller/
│       │   │   └── AdminController.java
│       │   └── service/
│       │       └── AdminQueryService.java
│       └── resources/
│           └── application.yml
├── frontend/
│   ├── index.html
│   ├── style.css
│   └── script.js
├── pom.xml
├── Dockerfile
├── docker-compose.yml
└── README.md
```

### Adding New Queries

1. **Add endpoint in `AdminController.java`:**
```java
@GetMapping("/users/custom")
public ResponseEntity<List<Map<String, Object>>> getCustomUsers() {
    return ResponseEntity.ok(adminQueryService.getCustomUsers());
}
```

2. **Implement query in `AdminQueryService.java`:**
```java
public List<Map<String, Object>> getCustomUsers() {
    String query = "MATCH (u:User) WHERE u.customField = 'value' RETURN u";
    return executeQuery(query);
}
```

3. **Add frontend button in `index.html`:**
```html
<button class="query-btn" onclick="queryCustomUsers()">Custom Users</button>
```

4. **Add JavaScript function in `script.js`:**
```javascript
async function queryCustomUsers() {
    await executeAPICall('/users/custom');
}
```

## 🐳 Docker Deployment

### Build Image

```bash
docker build -t stageminder-admin-console .
```

### Run Container

```bash
docker run -d \
  --name admin-console \
  -p 8081:8081 \
  -e NEO4J_URI=bolt://localhost:7687 \
  -e NEO4J_PASSWORD=stageminder2024 \
  stageminder-admin-console
```

## 🔒 Security Notes

- ⚠️ This is a development/admin tool
- 🚫 Do not expose to public networks
- 🔐 Use proper authentication in production
- 🛡️ Destructive queries are blocked in the service layer

## 🧪 Testing

### Health Check

```bash
curl http://localhost:8081/api/admin/health
```

### Test Queries

```bash
# Get all users
curl http://localhost:8081/api/admin/users

# Get user statistics
curl http://localhost:8081/api/admin/users/stats
```

## 📝 API Documentation

Once running, visit:
- **Actuator Health:** http://localhost:8081/actuator/health
- **Application Info:** http://localhost:8081/actuator/info

## 🚨 Troubleshooting

### Common Issues

1. **Connection Failed:**
   - Ensure Neo4j is running on localhost:7474
   - Check Neo4j credentials in application.yml

2. **CORS Errors:**
   - Ensure backend is running on port 8081
   - Check CORS configuration in CorsConfig.java

3. **Maven Build Fails:**
   - Ensure Java 17+ is installed
   - Clear Maven cache: `mvn clean`

4. **Port Already in Use:**
   - Change port in application.yml
   - Update frontend API URL in script.js

### Logs

```bash
# View application logs
tail -f logs/spring.log

# View Docker logs
docker logs stageminder-admin-console
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is part of the StageMinder application suite.