# StageMinder Neo4j 数据库结构文档

## 概述
StageMinder 使用 Neo4j 图数据库来存储演出者(Acts)、场地(Venues)、用户(Users)等信息及其关系。

## 核心概念
- **节点(Nodes)**: 代表实体，如用户、演出者、场地等
- **关系(Relationships)**: 连接节点，表示实体间的关联
- **属性(Properties)**: 节点和关系的数据字段

## 主要节点类型(Node Labels)

### 1. Profile (演出者/场地档案)
**用途**: 存储演出者和场地的基本信息
**类型**: 
- `ACT_PROFILE`: 演出者档案
- `VIRTUAL_ACT_PROFILE`: 虚拟演出者档案
- `VENUE_PROFILE`: 场地档案
- `VIRTUAL_VENUE_PROFILE`: 虚拟场地档案

**主要属性**:
| 属性名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| profileId | String | 唯一标识符 | "act_search_001" |
| profileName | String | 档案名称 | "The Electric Vibes Band" |
| profileEmail | String | 联系邮箱 | "<EMAIL>" |
| profileRole | String | 角色描述 | "Lead Performer" |
| profileType | Enum | 档案类型 | "ACT_PROFILE" |
| status | Enum | 状态 | "STATUS_PUBLISHED" |
| numMembers | Integer | 成员数量 | 4 |
| numberOfFollowers | Integer | 关注者数量 | 2500 |
| gigsPerMonth | Double | 月演出次数 | 12.0 |
| averageGigsPrice | Double | 平均演出价格 | 3500.0 |
| numberOfGigs | Integer | 总演出次数 | 85 |
| numberOfBookings | Integer | 总预订次数 | 92 |
| performanceLanguages | List<String> | 演出语言 | ["English"] |
| communicationLanguages | List<String> | 沟通语言 | ["English"] |
| preferredLanguage | String | 首选语言 | "English" |

### 2. User (用户)
**用途**: 存储系统用户信息

**主要属性**:
| 属性名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| firstName | String | 名 | "John" |
| lastName | String | 姓 | "Doe" |
| email | String | 邮箱 | "<EMAIL>" |
| password | String | 密码(加密) | "encrypted_password" |
| role | String | 用户角色 | "USER" |
| isEnabled | Boolean | 是否启用 | true |
| phoneNumber | String | 电话号码 | "+1234567890" |

### 3. Location (位置)
**用途**: 存储地理位置信息

**主要属性**:
| 属性名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| country | String | 国家 | "Canada" |
| state | String | 省/州 | "Ontario" |
| city | String | 城市 | "Toronto" |
| streetAddress | String | 街道地址 | "123 Music Street" |
| zipCode | String | 邮编 | "M5V 3A8" |
| latitude | Double | 纬度 | 43.6532 |
| longitude | Double | 经度 | -79.3832 |
| canTravelLongDistance | Boolean | 可长途旅行 | true |

### 4. ProfileInfo (档案详细信息)
**用途**: 存储档案的详细描述信息

**主要属性**:
| 属性名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| bio | String | 简介 | "High-energy rock band..." |
| suitableForChildren | Boolean | 适合儿童 | true |
| suitableForAdultsOnly | Boolean | 仅限成人 | false |
| socialMediaLinks | List<String> | 社交媒体链接 | ["https://facebook.com/..."] |

### 5. ProfilePayments (支付信息)
**用途**: 存储价格和支付方式信息

**主要属性**:
| 属性名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| currency | String | 货币 | "CAD" |
| typicalPrice | Integer | 典型价格 | 3500 |
| minimumPrice | Integer | 最低价格 | 2500 |
| minPriceChargingType | Enum | 最低价格计费类型 | "EVENT" |
| typicalPriceChargingType | Enum | 典型价格计费类型 | "EVENT" |
| acceptablePaymentMethods | List<String> | 接受的支付方式 | ["Cash", "Credit Card"] |
| forRent | Boolean | 可租用 | false |

### 6. ProfileMedia (媒体文件)
**用途**: 存储图片、视频、音频链接

**主要属性**:
| 属性名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| imageUrls | List<String> | 图片链接 | ["https://images.unsplash.com/..."] |
| videoUrls | List<String> | 视频链接 | ["https://youtube.com/..."] |
| audioUrls | List<String> | 音频链接 | ["https://soundcloud.com/..."] |

### 7. ProfileRating (评分)
**用途**: 存储评分和评价信息

**主要属性**:
| 属性名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| overallRating | Double | 总体评分 | 4.7 |
| numberOfRatings | Integer | 评分数量 | 45 |
| professionalismRating | Double | 专业性评分 | 4.8 |
| entertainmentValueRating | Double | 娱乐价值评分 | 4.6 |
| drawRating | Double | 吸引力评分 | 4.7 |
| isGoldBannerMember | Boolean | 金牌会员 | false |

## 主要关系类型(Relationship Types)

### 核心关系
| 关系名 | 起始节点 | 目标节点 | 描述 |
|--------|----------|----------|------|
| IS_LOCATED_AT | Profile | Location | 档案位于某地 |
| HAS_INFO | Profile | ProfileInfo | 档案包含详细信息 |
| HAS_PAYMENTS | Profile | ProfilePayments | 档案包含支付信息 |
| HAS_MEDIA | Profile | ProfileMedia | 档案包含媒体文件 |
| HAS_ACT_RATING | Profile | ProfileRating | 档案包含评分 |
| HAS_MESSAGE_BOX | User | MessageBox | 用户拥有消息箱 |

### 地理关系
| 关系名 | 起始节点 | 目标节点 | 描述 |
|--------|----------|----------|------|
| HAS_COUNTRY | SupportedRegions | Country | 支持的国家 |
| HAS_STATE | Country | State | 国家包含州/省 |
| HAS_CITY | State | City | 州/省包含城市 |

### 分类关系
| 关系名 | 起始节点 | 目标节点 | 描述 |
|--------|----------|----------|------|
| HAS_ENTERTAINMENT_TYPE | Profile/SupportedEntertainmentTypes | EntertainmentType | 娱乐类型 |
| HAS_GENRE | Profile | MusicGenre | 音乐类型 |
| HAS_MEMBER | EntertainmentType | EntertainmentTypeMember | 娱乐类型成员 |
| HAS_TRANSLATIONS | Various | Translations | 多语言翻译 |

## 枚举值定义

### ProfileType (档案类型)
- `ACT_PROFILE`: 演出者档案
- `VIRTUAL_ACT_PROFILE`: 虚拟演出者档案  
- `VENUE_PROFILE`: 场地档案
- `VIRTUAL_VENUE_PROFILE`: 虚拟场地档案

### ProfileStatus (档案状态)
- `STATUS_CREATED`: 已创建
- `STATUS_PUBLISHED`: 已发布
- `STATUS_DRAFT`: 草稿

### ChargingType (计费类型)
- `HOURLY`: 按小时计费
- `EVENT`: 按活动计费

## Neo4j 基础操作指南

### 连接数据库
```bash
# 连接到Docker中的Neo4j
docker exec stageminder-neo4j cypher-shell -u neo4j -p stageminder2024
```

### 基本查询语法

#### 1. 查询操作 (READ)

**查看所有节点标签**
```cypher
CALL db.labels() YIELD label RETURN label ORDER BY label;
```

**查看所有关系类型**
```cypher
CALL db.relationshipTypes() YIELD relationshipType RETURN relationshipType ORDER BY relationshipType;
```

**查询所有演出者**
```cypher
MATCH (p:Profile)
WHERE p.profileType IN ['ACT_PROFILE', 'VIRTUAL_ACT_PROFILE']
RETURN p.profileName, p.profileId, p.status
ORDER BY p.profileName;
```

**查询所有场地**
```cypher
MATCH (p:Profile)
WHERE p.profileType IN ['VENUE_PROFILE', 'VIRTUAL_VENUE_PROFILE']
RETURN p.profileName, p.profileId, p.status
ORDER BY p.profileName;
```

**查询特定档案的完整信息**
```cypher
MATCH (p:Profile {profileId: 'act_search_001'})
OPTIONAL MATCH (p)-[:IS_LOCATED_AT]->(loc:Location)
OPTIONAL MATCH (p)-[:HAS_INFO]->(info:ProfileInfo)
OPTIONAL MATCH (p)-[:HAS_PAYMENTS]->(pay:ProfilePayments)
OPTIONAL MATCH (p)-[:HAS_MEDIA]->(media:ProfileMedia)
OPTIONAL MATCH (p)-[:HAS_ACT_RATING]->(rating:ProfileRating)
RETURN p, loc, info, pay, media, rating;
```

**按地理位置查询**
```cypher
MATCH (p:Profile)-[:IS_LOCATED_AT]->(loc:Location)
WHERE loc.city = 'Toronto'
RETURN p.profileName, p.profileType, loc.streetAddress;
```

**按评分查询**
```cypher
MATCH (p:Profile)-[:HAS_ACT_RATING]->(r:ProfileRating)
WHERE r.overallRating >= 4.5
RETURN p.profileName, r.overallRating, r.numberOfRatings
ORDER BY r.overallRating DESC;
```

#### 2. 创建操作 (CREATE)

**创建演出者档案**
```cypher
CREATE (p:Profile {
  profileId: 'new_act_001',
  profileName: 'New Band Name',
  profileEmail: '<EMAIL>',
  profileRole: 'Lead Performer',
  profileType: 'ACT_PROFILE',
  status: 'STATUS_PUBLISHED',
  numMembers: 3,
  numberOfFollowers: 0,
  gigsPerMonth: 0.0,
  averageGigsPrice: 0.0,
  numberOfGigs: 0,
  numberOfBookings: 0,
  performanceLanguages: ['English'],
  communicationLanguages: ['English'],
  preferredLanguage: 'English'
})
RETURN p.profileName;
```

**创建场地档案**
```cypher
CREATE (p:Profile {
  profileId: 'new_venue_001',
  profileName: 'New Venue Name',
  profileEmail: '<EMAIL>',
  profileRole: 'Venue Manager',
  profileType: 'VENUE_PROFILE',
  status: 'STATUS_PUBLISHED',
  numMembers: 1,
  numberOfFollowers: 0,
  gigsPerMonth: 0.0,
  averageGigsPrice: 0.0,
  numberOfGigs: 0,
  numberOfBookings: 0,
  performanceLanguages: ['English'],
  communicationLanguages: ['English'],
  preferredLanguage: 'English'
})
RETURN p.profileName;
```

**创建完整的档案(包含所有关系)**
```cypher
CREATE (p:Profile {
  profileId: 'complete_profile_001',
  profileName: 'Complete Profile',
  profileType: 'ACT_PROFILE',
  status: 'STATUS_PUBLISHED'
}),
(loc:Location {
  country: 'Canada',
  city: 'Toronto',
  latitude: 43.6532,
  longitude: -79.3832
}),
(info:ProfileInfo {
  bio: 'Profile description',
  suitableForChildren: true,
  suitableForAdultsOnly: false
}),
(pay:ProfilePayments {
  currency: 'CAD',
  typicalPrice: 3000,
  minimumPrice: 2000,
  forRent: false
}),
(media:ProfileMedia {
  imageUrls: ['https://example.com/image.jpg'],
  videoUrls: [],
  audioUrls: []
}),
(rating:ProfileRating {
  overallRating: 0.0,
  numberOfRatings: 0,
  professionalismRating: 0.0,
  entertainmentValueRating: 0.0,
  drawRating: 0.0
}),
(p)-[:IS_LOCATED_AT]->(loc),
(p)-[:HAS_INFO]->(info),
(p)-[:HAS_PAYMENTS]->(pay),
(p)-[:HAS_MEDIA]->(media),
(p)-[:HAS_ACT_RATING]->(rating)
RETURN p.profileName;
```

#### 3. 更新操作 (UPDATE)

**更新档案基本信息**
```cypher
MATCH (p:Profile {profileId: 'act_search_001'})
SET p.profileName = 'Updated Band Name',
    p.numberOfFollowers = 3000,
    p.averageGigsPrice = 4000.0
RETURN p.profileName, p.numberOfFollowers, p.averageGigsPrice;
```

**更新档案状态**
```cypher
MATCH (p:Profile {profileId: 'act_search_001'})
SET p.status = 'STATUS_PUBLISHED'
RETURN p.profileName, p.status;
```

**更新位置信息**
```cypher
MATCH (p:Profile {profileId: 'act_search_001'})-[:IS_LOCATED_AT]->(loc:Location)
SET loc.city = 'Vancouver',
    loc.state = 'British Columbia',
    loc.latitude = 49.2827,
    loc.longitude = -123.1207
RETURN p.profileName, loc.city, loc.state;
```

**更新评分信息**
```cypher
MATCH (p:Profile {profileId: 'act_search_001'})-[:HAS_ACT_RATING]->(r:ProfileRating)
SET r.overallRating = 4.8,
    r.numberOfRatings = 50,
    r.professionalismRating = 4.9,
    r.entertainmentValueRating = 4.7,
    r.drawRating = 4.8
RETURN p.profileName, r.overallRating, r.numberOfRatings;
```

**添加社交媒体链接**
```cypher
MATCH (p:Profile {profileId: 'act_search_001'})-[:HAS_INFO]->(info:ProfileInfo)
SET info.socialMediaLinks = info.socialMediaLinks + ['https://twitter.com/newlink']
RETURN p.profileName, info.socialMediaLinks;
```

#### 4. 删除操作 (DELETE)

**删除特定档案及其所有关系**
```cypher
MATCH (p:Profile {profileId: 'profile_to_delete'})
OPTIONAL MATCH (p)-[r]->(related)
DELETE r, p, related
RETURN 'Profile deleted successfully';
```

**只删除档案节点(保留相关节点)**
```cypher
MATCH (p:Profile {profileId: 'profile_to_delete'})
DETACH DELETE p
RETURN 'Profile deleted successfully';
```

**删除特定关系**
```cypher
MATCH (p:Profile {profileId: 'act_search_001'})-[r:HAS_MEDIA]->(media:ProfileMedia)
DELETE r, media
RETURN 'Media relationship deleted';
```

**批量删除(谨慎使用)**
```cypher
MATCH (p:Profile)
WHERE p.status = 'STATUS_DRAFT' AND p.profileType = 'ACT_PROFILE'
DETACH DELETE p
RETURN 'Draft profiles deleted';
```

#### 5. 复杂查询示例

**查找附近的场地**
```cypher
MATCH (venue:Profile)-[:IS_LOCATED_AT]->(vLoc:Location)
WHERE venue.profileType IN ['VENUE_PROFILE', 'VIRTUAL_VENUE_PROFILE']
  AND venue.status = 'STATUS_PUBLISHED'
  AND point.distance(
    point({latitude: vLoc.latitude, longitude: vLoc.longitude}),
    point({latitude: 43.6532, longitude: -79.3832})
  ) < 50000  // 50km radius
RETURN venue.profileName, vLoc.city, vLoc.streetAddress
ORDER BY point.distance(
  point({latitude: vLoc.latitude, longitude: vLoc.longitude}),
  point({latitude: 43.6532, longitude: -79.3832})
);
```

**查找高评分演出者**
```cypher
MATCH (act:Profile)-[:HAS_ACT_RATING]->(rating:ProfileRating)
WHERE act.profileType IN ['ACT_PROFILE', 'VIRTUAL_ACT_PROFILE']
  AND act.status = 'STATUS_PUBLISHED'
  AND rating.overallRating >= 4.5
  AND rating.numberOfRatings >= 10
RETURN act.profileName, rating.overallRating, rating.numberOfRatings,
       act.averageGigsPrice, act.numberOfFollowers
ORDER BY rating.overallRating DESC, rating.numberOfRatings DESC;
```

**统计查询**
```cypher
MATCH (p:Profile)
RETURN p.profileType as type,
       count(p) as count,
       avg(p.numberOfFollowers) as avgFollowers,
       avg(p.averageGigsPrice) as avgPrice
ORDER BY count DESC;
```

## 常用查询模式

### 1. 档案搜索
```cypher
// 按名称搜索
MATCH (p:Profile)
WHERE toLower(p.profileName) CONTAINS toLower('rock')
RETURN p.profileName, p.profileType;

// 按地理位置搜索
MATCH (p:Profile)-[:IS_LOCATED_AT]->(loc:Location)
WHERE loc.city = 'Toronto' OR loc.state = 'Ontario'
RETURN p.profileName, loc.city, loc.state;

// 按价格范围搜索
MATCH (p:Profile)-[:HAS_PAYMENTS]->(pay:ProfilePayments)
WHERE pay.typicalPrice >= 2000 AND pay.typicalPrice <= 5000
RETURN p.profileName, pay.typicalPrice, pay.currency;
```

### 2. 关系查询
```cypher
// 查看档案的所有关系
MATCH (p:Profile {profileId: 'act_search_001'})-[r]->(related)
RETURN type(r) as relationship, labels(related) as nodeType, related;

// 查找没有评分的档案
MATCH (p:Profile)
WHERE NOT (p)-[:HAS_ACT_RATING]->()
RETURN p.profileName, p.profileType;

// 查找没有支付信息的场地
MATCH (p:Profile)
WHERE p.profileType IN ['VENUE_PROFILE', 'VIRTUAL_VENUE_PROFILE']
  AND NOT (p)-[:HAS_PAYMENTS]->()
RETURN p.profileName;
```

## 数据库维护和最佳实践

### 1. 索引管理
```cypher
// 创建索引以提高查询性能
CREATE INDEX profile_id_index FOR (p:Profile) ON (p.profileId);
CREATE INDEX profile_type_index FOR (p:Profile) ON (p.profileType);
CREATE INDEX profile_status_index FOR (p:Profile) ON (p.status);

// 查看现有索引
SHOW INDEXES;

// 删除索引
DROP INDEX profile_id_index;
```

### 2. 约束管理
```cypher
// 创建唯一性约束
CREATE CONSTRAINT profile_id_unique FOR (p:Profile) REQUIRE p.profileId IS UNIQUE;

// 查看现有约束
SHOW CONSTRAINTS;

// 删除约束
DROP CONSTRAINT profile_id_unique;
```

### 3. 数据备份和恢复
```bash
# 导出数据
docker exec stageminder-neo4j neo4j-admin dump --database=neo4j --to=/backups/neo4j-backup.dump

# 导入数据
docker exec stageminder-neo4j neo4j-admin load --from=/backups/neo4j-backup.dump --database=neo4j --force
```

### 4. 性能优化建议

**查询优化**:
- 使用 `PROFILE` 或 `EXPLAIN` 分析查询性能
- 为经常查询的属性创建索引
- 避免使用 `MATCH (n) RETURN n` 这样的全表扫描
- 使用 `LIMIT` 限制返回结果数量

**示例**:
```cypher
// 分析查询性能
PROFILE MATCH (p:Profile {profileType: 'ACT_PROFILE'}) RETURN p.profileName;

// 使用LIMIT限制结果
MATCH (p:Profile) RETURN p.profileName LIMIT 10;
```

## 常见错误和解决方案

### 1. 关系不存在错误
```cypher
// 错误: 假设关系存在
MATCH (p:Profile)-[:HAS_PAYMENTS]->(pay:ProfilePayments)
WHERE p.profileId = 'some_id'
RETURN pay.typicalPrice;

// 正确: 使用OPTIONAL MATCH
MATCH (p:Profile {profileId: 'some_id'})
OPTIONAL MATCH (p)-[:HAS_PAYMENTS]->(pay:ProfilePayments)
RETURN p.profileName, pay.typicalPrice;
```

### 2. 数据类型错误
```cypher
// 确保数据类型正确
SET p.numberOfFollowers = toInteger('1000')  // 字符串转整数
SET p.averageGigsPrice = toFloat('3500.50')  // 字符串转浮点数
```

### 3. 空值处理
```cypher
// 处理空值
MATCH (p:Profile)
WHERE p.profileName IS NOT NULL
RETURN p.profileName;

// 使用COALESCE提供默认值
MATCH (p:Profile)
RETURN p.profileName, COALESCE(p.numberOfFollowers, 0) as followers;
```

## 数据模型图

```
User ──[HAS]──> Profile ──[IS_LOCATED_AT]──> Location
                  │
                  ├──[HAS_INFO]──> ProfileInfo
                  ├──[HAS_PAYMENTS]──> ProfilePayments
                  ├──[HAS_MEDIA]──> ProfileMedia
                  ├──[HAS_ACT_RATING]──> ProfileRating
                  └──[HAS_ENTERTAINMENT_TYPE]──> EntertainmentType
                                                      │
                                                      └──[HAS_MEMBER]──> EntertainmentTypeMember

Country ──[HAS_STATE]──> State ──[HAS_CITY]──> City
```

## 总结

StageMinder的Neo4j数据库采用图数据库的优势来存储和查询复杂的关系数据。主要特点：

1. **灵活的数据模型**: 可以轻松添加新的节点类型和关系
2. **高效的关系查询**: 图数据库天然适合处理复杂的关系查询
3. **可扩展性**: 支持水平和垂直扩展
4. **ACID事务**: 保证数据一致性

使用本文档时，请注意：
- 在生产环境中执行删除操作前务必备份数据
- 定期监控查询性能并优化索引
- 遵循命名约定保持数据库结构清晰
- 使用事务确保数据一致性

如需更多帮助，请参考 [Neo4j官方文档](https://neo4j.com/docs/) 或联系开发团队。
```
```
