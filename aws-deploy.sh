#!/bin/bash

# =============================================================================
# StageMinder AWS 一键部署脚本
# 服务器IP: ***********
# 此脚本将自动部署完整的StageMinder系统到AWS服务器
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# AWS服务器配置
AWS_SERVER_IP="***********"
PROJECT_NAME="StageMinder"

# 函数定义
print_header() {
    echo -e "${CYAN}"
    echo "=================================================="
    echo "🚀 StageMinder AWS 一键部署脚本"
    echo "📍 服务器: $AWS_SERVER_IP"
    echo "📅 时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "=================================================="
    echo -e "${NC}"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 检查系统环境
check_environment() {
    print_step "检查系统环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，正在安装..."
        curl -fsSL https://get.docker.com -o get-docker.sh
        sudo sh get-docker.sh
        sudo usermod -aG docker $USER
        print_success "Docker 安装完成"
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose 未安装，正在安装..."
        sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        sudo chmod +x /usr/local/bin/docker-compose
        print_success "Docker Compose 安装完成"
    fi
    
    # 检查Docker服务
    if ! docker info > /dev/null 2>&1; then
        print_warning "Docker 服务未运行，正在启动..."
        sudo systemctl start docker
        sudo systemctl enable docker
    fi
    
    # 检查权限
    if ! docker ps > /dev/null 2>&1; then
        print_warning "当前用户无Docker权限，请重新登录或运行: newgrp docker"
        newgrp docker
    fi
    
    print_success "环境检查完成"
}

# 配置AWS环境变量
configure_aws_environment() {
    print_step "配置AWS环境变量..."
    
    # 创建.env文件
    cat > .env.aws << EOF
# AWS生产环境配置
AWS_ACCESS_KEY_ID=\${AWS_ACCESS_KEY_ID:-your_aws_access_key_here}
AWS_SECRET_ACCESS_KEY=\${AWS_SECRET_ACCESS_KEY:-your_aws_secret_key_here}
AWS_REGION=us-east-1
S3_BUCKET_NAME=\${S3_BUCKET_NAME:-your_s3_bucket_name_here}

# 服务器配置
SERVER_IP=$AWS_SERVER_IP
PUBLIC_DOMAIN=$AWS_SERVER_IP

# Neo4j安全配置
NEO4J_PASSWORD=stageminder2024_aws_prod

# 应用配置
NODE_ENV=production
SPRING_PROFILES_ACTIVE=prod
EOF
    
    print_info "请确保设置以下环境变量："
    echo "  export AWS_ACCESS_KEY_ID='your_actual_access_key'"
    echo "  export AWS_SECRET_ACCESS_KEY='your_actual_secret_key'"
    echo "  export S3_BUCKET_NAME='your_actual_bucket_name'"
    echo ""
    
    # 检查环境变量
    if [[ -z "$AWS_ACCESS_KEY_ID" ]] || [[ "$AWS_ACCESS_KEY_ID" == "your_aws_access_key_here" ]]; then
        print_warning "AWS_ACCESS_KEY_ID 未设置，使用默认值"
    fi
    
    if [[ -z "$S3_BUCKET_NAME" ]] || [[ "$S3_BUCKET_NAME" == "your_s3_bucket_name_here" ]]; then
        print_warning "S3_BUCKET_NAME 未设置，使用默认值"
    fi
    
    print_success "AWS环境配置完成"
}

# 更新Docker配置文件
update_docker_configs() {
    print_step "更新Docker配置文件..."
    
    # 备份原始文件
    cp docker-compose.main.yml docker-compose.main.yml.backup 2>/dev/null || true
    
    # 更新主配置文件中的IP地址
    sed -i "s|http://localhost|http://$AWS_SERVER_IP|g" docker-compose.main.yml
    sed -i "s|NEXT_PUBLIC_API_URL=.*|NEXT_PUBLIC_API_URL=http://$AWS_SERVER_IP/api|g" docker-compose.main.yml
    
    # 更新AWS环境变量
    if [[ -n "$AWS_ACCESS_KEY_ID" ]] && [[ "$AWS_ACCESS_KEY_ID" != "your_aws_access_key_here" ]]; then
        sed -i "s|AWS_ACCESS_KEY_ID=.*|AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID|g" docker-compose.main.yml
    fi
    
    if [[ -n "$AWS_SECRET_ACCESS_KEY" ]] && [[ "$AWS_SECRET_ACCESS_KEY" != "your_aws_secret_key_here" ]]; then
        sed -i "s|AWS_SECRET_ACCESS_KEY=.*|AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY|g" docker-compose.main.yml
    fi
    
    if [[ -n "$S3_BUCKET_NAME" ]] && [[ "$S3_BUCKET_NAME" != "your_s3_bucket_name_here" ]]; then
        sed -i "s|S3_BUCKET_NAME=.*|S3_BUCKET_NAME=$S3_BUCKET_NAME|g" docker-compose.main.yml
    fi
    
    # 更新StageMinder配置
    if [[ -f "StageMinder/docker-compose-optimized.yml" ]]; then
        cp StageMinder/docker-compose-optimized.yml StageMinder/docker-compose-optimized.yml.backup 2>/dev/null || true
        sed -i "s|http://localhost|http://$AWS_SERVER_IP|g" StageMinder/docker-compose-optimized.yml
        sed -i "s|NEXT_PUBLIC_API_URL=.*|NEXT_PUBLIC_API_URL=http://$AWS_SERVER_IP/api|g" StageMinder/docker-compose-optimized.yml
        
        if [[ -n "$AWS_ACCESS_KEY_ID" ]] && [[ "$AWS_ACCESS_KEY_ID" != "your_aws_access_key_here" ]]; then
            sed -i "s|AWS_ACCESS_KEY_ID=.*|AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID|g" StageMinder/docker-compose-optimized.yml
        fi
        
        if [[ -n "$AWS_SECRET_ACCESS_KEY" ]] && [[ "$AWS_SECRET_ACCESS_KEY" != "your_aws_secret_key_here" ]]; then
            sed -i "s|AWS_SECRET_ACCESS_KEY=.*|AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY|g" StageMinder/docker-compose-optimized.yml
        fi
        
        if [[ -n "$S3_BUCKET_NAME" ]] && [[ "$S3_BUCKET_NAME" != "your_s3_bucket_name_here" ]]; then
            sed -i "s|S3_BUCKET_NAME=.*|S3_BUCKET_NAME=$S3_BUCKET_NAME|g" StageMinder/docker-compose-optimized.yml
        fi
    fi
    
    print_success "Docker配置文件更新完成"
}

# 备份现有数据
backup_existing_data() {
    print_step "备份现有数据..."
    
    BACKUP_DIR="./aws_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 检查是否有现有的Neo4j容器
    if docker ps -a | grep -q "neo4j"; then
        print_info "发现现有Neo4j容器，正在备份..."
        
        # 查找Neo4j容器
        NEO4J_CONTAINER=$(docker ps -a --format "{{.Names}}" | grep neo4j | head -1)
        
        if [[ -n "$NEO4J_CONTAINER" ]]; then
            print_info "备份容器: $NEO4J_CONTAINER"
            
            # 创建数据导出
            docker exec "$NEO4J_CONTAINER" neo4j-admin dump --database=neo4j --to=/tmp/backup.dump 2>/dev/null || print_warning "数据导出失败（可能是新安装）"
            
            # 复制备份文件
            docker cp "$NEO4J_CONTAINER:/tmp/backup.dump" "$BACKUP_DIR/neo4j-backup.dump" 2>/dev/null || print_warning "备份文件复制失败"
            
            # 备份数据卷
            if docker volume ls | grep -q neo4j; then
                print_info "备份Neo4j数据卷..."
                docker run --rm -v stageminder_neo4j_data:/source -v "$(pwd)/$BACKUP_DIR":/backup alpine tar czf /backup/neo4j-volume-backup.tar.gz -C /source . 2>/dev/null || print_warning "数据卷备份失败"
            fi
        fi
        
        print_success "数据备份完成: $BACKUP_DIR"
    else
        print_info "未发现现有数据，跳过备份"
        rm -rf "$BACKUP_DIR"
    fi
}

# 停止现有服务
stop_existing_services() {
    print_step "停止现有服务..."
    
    # 使用docker-control.sh停止服务
    if [[ -f "./docker-control.sh" ]]; then
        chmod +x ./docker-control.sh
        ./docker-control.sh stop 2>/dev/null || print_warning "无法使用docker-control.sh停止服务"
    fi
    
    # 手动停止可能的服务
    docker-compose down 2>/dev/null || true
    docker-compose -f docker-compose.main.yml down 2>/dev/null || true
    docker-compose -f docker-compose.neo4j.yml down 2>/dev/null || true
    
    # 停止StageMinder子项目
    if [[ -d "StageMinder" ]]; then
        cd StageMinder
        docker-compose down 2>/dev/null || true
        docker-compose -f docker-compose-optimized.yml down 2>/dev/null || true
        cd ..
    fi
    
    # 停止AdminConsole
    if [[ -d "adminconsole" ]]; then
        cd adminconsole
        docker-compose down 2>/dev/null || true
        docker-compose -f docker-compose-optimized.yml down 2>/dev/null || true
        cd ..
    fi
    
    print_success "现有服务已停止"
}

# 构建和启动服务
deploy_services() {
    print_step "构建和启动服务..."
    
    # 给脚本添加执行权限
    chmod +x docker-control.sh 2>/dev/null || true
    
    # 拉取最新镜像
    print_info "拉取最新Docker镜像..."
    docker-compose -f docker-compose.main.yml pull 2>/dev/null || print_warning "部分镜像拉取失败"
    
    # 构建镜像
    print_info "构建应用镜像..."
    docker-compose -f docker-compose.main.yml build --no-cache
    
    # 启动服务
    print_info "启动完整服务栈..."
    if [[ -f "./docker-control.sh" ]]; then
        ./docker-control.sh start
    else
        docker-compose -f docker-compose.main.yml up -d
    fi
    
    print_success "服务部署完成"
}

# 等待服务启动
wait_for_services() {
    print_step "等待服务启动..."
    
    print_info "等待Neo4j启动（最多3分钟）..."
    for i in {1..36}; do
        if docker exec stageminder-neo4j-shared cypher-shell -u neo4j -p stageminder2024 "RETURN 1" > /dev/null 2>&1; then
            print_success "Neo4j启动成功！"
            break
        fi
        if [[ $i -eq 36 ]]; then
            print_error "Neo4j启动超时"
            return 1
        fi
        echo -n "."
        sleep 5
    done
    
    print_info "等待后端服务启动..."
    for i in {1..24}; do
        if curl -f "http://localhost:8080/actuator/health" > /dev/null 2>&1; then
            print_success "后端服务启动成功！"
            break
        fi
        if [[ $i -eq 24 ]]; then
            print_warning "后端服务启动超时"
        fi
        echo -n "."
        sleep 5
    done
    
    print_info "等待前端服务启动..."
    for i in {1..12}; do
        if curl -f "http://localhost:3000" > /dev/null 2>&1; then
            print_success "前端服务启动成功！"
            break
        fi
        if [[ $i -eq 12 ]]; then
            print_warning "前端服务启动超时"
        fi
        echo -n "."
        sleep 5
    done
    
    print_success "服务启动检查完成"
}

# 验证部署
verify_deployment() {
    print_step "验证部署状态..."
    
    echo ""
    print_info "=== 服务状态 ==="
    
    if [[ -f "./docker-control.sh" ]]; then
        ./docker-control.sh status
    else
        docker-compose -f docker-compose.main.yml ps
    fi
    
    echo ""
    print_info "=== 端口检查 ==="
    
    ports=(7474 7687 8080 8081 3000 3001 80)
    for port in "${ports[@]}"; do
        if ss -tuln | grep -q ":$port "; then
            echo -e "  ✅ 端口 $port: ${GREEN}开放${NC}"
        else
            echo -e "  ❌ 端口 $port: ${RED}未开放${NC}"
        fi
    done
    
    echo ""
    print_info "=== 健康检查 ==="
    
    # Neo4j检查
    if docker exec stageminder-neo4j-shared cypher-shell -u neo4j -p stageminder2024 "RETURN 1" > /dev/null 2>&1; then
        echo -e "  ✅ Neo4j: ${GREEN}正常${NC}"
    else
        echo -e "  ❌ Neo4j: ${RED}异常${NC}"
    fi
    
    # 后端检查
    if curl -f "http://localhost:8080/actuator/health" > /dev/null 2>&1; then
        echo -e "  ✅ 后端API: ${GREEN}正常${NC}"
    else
        echo -e "  ❌ 后端API: ${RED}异常${NC}"
    fi
    
    # 前端检查
    if curl -f "http://localhost:3000" > /dev/null 2>&1; then
        echo -e "  ✅ 前端: ${GREEN}正常${NC}"
    else
        echo -e "  ❌ 前端: ${RED}异常${NC}"
    fi
    
    # Admin Console检查
    if curl -f "http://localhost:8081/actuator/health" > /dev/null 2>&1; then
        echo -e "  ✅ Admin Console: ${GREEN}正常${NC}"
    else
        echo -e "  ❌ Admin Console: ${RED}异常${NC}"
    fi
}

# 显示访问信息
show_access_info() {
    print_step "部署完成！"
    
    echo ""
    echo -e "${GREEN}🎉 StageMinder 已成功部署到 AWS 服务器！${NC}"
    echo ""
    echo -e "${CYAN}📍 访问地址:${NC}"
    echo "  🌐 主应用:        http://$AWS_SERVER_IP"
    echo "  📊 Neo4j Browser: http://$AWS_SERVER_IP:7474"
    echo "  🔧 管理控制台:    http://$AWS_SERVER_IP:3001"
    echo "  📱 前端直接访问:  http://$AWS_SERVER_IP:3000"
    echo "  🔌 后端API:       http://$AWS_SERVER_IP:8080"
    echo "  ⚙️  Admin API:     http://$AWS_SERVER_IP:8081"
    echo ""
    echo -e "${CYAN}🔐 登录信息:${NC}"
    echo "  Neo4j用户名: neo4j"
    echo "  Neo4j密码:   stageminder2024"
    echo ""
    echo -e "${CYAN}📋 管理命令:${NC}"
    echo "  查看状态:    ./docker-control.sh status"
    echo "  查看日志:    ./docker-control.sh logs"
    echo "  重启服务:    ./docker-control.sh restart"
    echo "  停止服务:    ./docker-control.sh stop"
    echo ""
    echo -e "${YELLOW}⚠️  注意事项:${NC}"
    echo "  1. 确保AWS安全组开放了相应端口"
    echo "  2. 定期备份Neo4j数据"
    echo "  3. 监控服务器资源使用情况"
    echo "  4. 定期更新系统和Docker镜像"
    echo ""
}

# 错误处理
handle_error() {
    print_error "部署过程中发生错误！"
    echo ""
    print_info "故障排除建议："
    echo "  1. 检查Docker服务状态: sudo systemctl status docker"
    echo "  2. 查看容器日志: docker logs <container_name>"
    echo "  3. 检查端口占用: ss -tuln | grep <port>"
    echo "  4. 查看系统资源: free -h && df -h"
    echo "  5. 重新运行脚本: ./aws-deploy.sh"
    echo ""
    print_info "如需帮助，请检查日志文件或联系技术支持"
    exit 1
}

# 主函数
main() {
    # 设置错误处理
    trap handle_error ERR
    
    print_header
    
    # 检查是否以root权限运行
    if [[ $EUID -eq 0 ]]; then
        print_warning "不建议以root权限运行此脚本"
        print_info "建议使用普通用户运行，脚本会在需要时提示输入sudo密码"
    fi
    
    # 执行部署步骤
    check_environment
    configure_aws_environment
    update_docker_configs
    backup_existing_data
    stop_existing_services
    deploy_services
    wait_for_services
    verify_deployment
    show_access_info
    
    print_success "AWS部署脚本执行完成！"
}

# 帮助信息
show_help() {
    echo "StageMinder AWS 一键部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  --skip-backup  跳过数据备份步骤"
    echo "  --quick        快速部署（跳过验证步骤）"
    echo ""
    echo "环境变量:"
    echo "  AWS_ACCESS_KEY_ID      AWS访问密钥ID"
    echo "  AWS_SECRET_ACCESS_KEY  AWS访问密钥"
    echo "  S3_BUCKET_NAME         S3存储桶名称"
    echo ""
    echo "示例:"
    echo "  # 基本部署"
    echo "  $0"
    echo ""
    echo "  # 设置环境变量后部署"
    echo "  export AWS_ACCESS_KEY_ID='your_key'"
    echo "  export AWS_SECRET_ACCESS_KEY='your_secret'"
    echo "  export S3_BUCKET_NAME='your_bucket'"
    echo "  $0"
}

# 命令行参数处理
case "$1" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
