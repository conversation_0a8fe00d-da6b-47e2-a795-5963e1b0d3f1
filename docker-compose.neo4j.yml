# Independent Neo4j Service for StageMinder Projects
# Usage: docker-compose -f docker-compose.neo4j.yml up -d

services:
  neo4j:
    image: neo4j:5.15-community
    container_name: stageminder-neo4j-shared
    environment:
      # Authentication
      - NEO4J_AUTH=neo4j/stageminder2024
      
      # Plugins and Security
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      
      # Memory Configuration
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_memory_pagecache_size=512m
      
      # Network Configuration
      - NEO4J_dbms_connector_bolt_listen__address=0.0.0.0:7687
      - NEO4J_dbms_connector_http_listen__address=0.0.0.0:7474
      - NEO4J_dbms_connector_https_listen__address=0.0.0.0:7473
      
      # Performance Settings
      - NEO4J_dbms_default__database=neo4j
      - NEO4J_dbms_logs_query_enabled=INFO
      
    ports:
      - "7474:7474"   # HTTP Browser Interface
      - "7687:7687"   # Bolt Protocol
      - "7473:7473"   # HTTPS (optional)
      
    volumes:
      # Data persistence
      - stageminder_neo4j_data:/data
      - stageminder_neo4j_logs:/logs
      - stageminder_neo4j_conf:/conf
      - stageminder_neo4j_import:/var/lib/neo4j/import
      - stageminder_neo4j_plugins:/plugins
      
    networks:
      - stageminder-shared-network
      
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD-SHELL", "cypher-shell -u neo4j -p stageminder2024 'RETURN 1'"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

volumes:
  stageminder_neo4j_data:
    driver: local
    name: stageminder_neo4j_data
  stageminder_neo4j_logs:
    driver: local  
    name: stageminder_neo4j_logs
  stageminder_neo4j_conf:
    driver: local
    name: stageminder_neo4j_conf
  stageminder_neo4j_import:
    driver: local
    name: stageminder_neo4j_import
  stageminder_neo4j_plugins:
    driver: local
    name: stageminder_neo4j_plugins

networks:
  stageminder-shared-network:
    driver: bridge
    name: stageminder-shared-network
