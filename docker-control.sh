#!/bin/bash

# StageMinder Docker Control Script
# This script provides convenient commands to manage the entire StageMinder ecosystem
# with shared Neo4j database

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker Desktop first."
        exit 1
    fi
}

# Function to start Neo4j only
start_neo4j() {
    print_info "Starting shared Neo4j database..."
    docker-compose -f docker-compose.neo4j.yml up -d
    
    print_info "Waiting for Neo4j to be ready..."
    sleep 10
    
    # Wait for Neo4j to be healthy
    for i in {1..30}; do
        if docker exec stageminder-neo4j-shared cypher-shell -u neo4j -p stageminder2024 "RETURN 1" > /dev/null 2>&1; then
            print_success "Neo4j is ready!"
            return 0
        fi
        print_info "Waiting for Neo4j... ($i/30)"
        sleep 2
    done
    
    print_error "Neo4j failed to start properly"
    return 1
}

# Function to start everything
start_all() {
    check_docker
    print_info "Starting complete StageMinder system..."
    docker-compose -f docker-compose.main.yml up -d
    print_success "All services started!"
    print_info "Services will be available at:"
    echo "  • Neo4j Browser: http://localhost:7474"
    echo "  • StageMinder App: http://localhost"
    echo "  • StageMinder API: http://localhost:8080"
    echo "  • Admin Console API: http://localhost:8081"
    echo "  • Admin Console UI: http://localhost:3001"
}

# Function to start only StageMinder
start_stageminder() {
    check_docker
    print_info "Starting StageMinder application..."
    
    # Check if Neo4j is running
    if ! docker ps | grep -q "stageminder-neo4j-shared"; then
        print_warning "Neo4j is not running. Starting Neo4j first..."
        start_neo4j
    fi
    
    cd StageMinder
    docker-compose -f docker-compose-optimized.yml up -d
    cd ..
    print_success "StageMinder started!"
    print_info "Available at: http://localhost"
}

# Function to start only Admin Console
start_admin() {
    check_docker
    print_info "Starting Admin Console..."
    
    # Check if Neo4j is running
    if ! docker ps | grep -q "stageminder-neo4j-shared"; then
        print_warning "Neo4j is not running. Starting Neo4j first..."
        start_neo4j
    fi
    
    cd adminconsole
    docker-compose -f docker-compose-optimized.yml up -d
    cd ..
    print_success "Admin Console started!"
    print_info "Available at:"
    echo "  • Admin Console API: http://localhost:8081"
    echo "  • Admin Console UI: http://localhost:3001"
}

# Function to start development environment
start_dev() {
    check_docker
    print_info "Starting development environment..."
    
    # Start Neo4j first
    if ! docker ps | grep -q "stageminder-neo4j-shared"; then
        start_neo4j
    fi
    
    # Start StageMinder in development mode
    cd StageMinder
    docker-compose -f docker-compose-local-optimized.yml up -d
    cd ..
    
    # Start Admin Console
    cd adminconsole
    docker-compose -f docker-compose-optimized.yml up -d
    cd ..
    
    print_success "Development environment started!"
    print_info "Services available at:"
    echo "  • Neo4j Browser: http://localhost:7474"
    echo "  • StageMinder (dev): http://localhost"
    echo "  • Admin Console: http://localhost:3001"
}

# Function to stop all services
stop_all() {
    print_info "Stopping all StageMinder services..."
    
    # Stop main compose services
    docker-compose -f docker-compose.main.yml down 2>/dev/null || true
    
    # Stop individual services
    cd StageMinder 2>/dev/null && {
        docker-compose -f docker-compose-optimized.yml down 2>/dev/null || true
        docker-compose -f docker-compose-local-optimized.yml down 2>/dev/null || true
        cd ..
    }
    
    cd adminconsole 2>/dev/null && {
        docker-compose -f docker-compose-optimized.yml down 2>/dev/null || true
        cd ..
    }
    
    # Stop Neo4j
    docker-compose -f docker-compose.neo4j.yml down 2>/dev/null || true
    
    print_success "All services stopped!"
}

# Function to show status
show_status() {
    print_info "StageMinder Services Status:"
    echo ""
    
    containers=("stageminder-neo4j-shared" "stageminder-backend" "stageminder-frontend" "stageminder-nginx" "stageminder-admin-console" "admin-frontend")
    
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$container"; then
            status=$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep "$container" | awk '{print $2, $3, $4}')
            echo -e "  ✅ $container: ${GREEN}$status${NC}"
        else
            echo -e "  ❌ $container: ${RED}Not running${NC}"
        fi
    done
    
    echo ""
    print_info "Access URLs:"
    echo "  • Neo4j Browser: http://localhost:7474 (neo4j/stageminder2024)"
    echo "  • StageMinder: http://localhost"
    echo "  • Admin Console: http://localhost:3001"
}

# Function to show logs
show_logs() {
    if [ -z "$2" ]; then
        print_info "Showing logs for all services..."
        docker-compose -f docker-compose.main.yml logs -f
    else
        print_info "Showing logs for $2..."
        docker logs -f "$2" 2>/dev/null || docker-compose -f docker-compose.main.yml logs -f "$2"
    fi
}

# Function to cleanup volumes
cleanup() {
    print_warning "This will remove all Neo4j data! Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        stop_all
        print_info "Removing Docker volumes..."
        docker volume rm stageminder_neo4j_data stageminder_neo4j_logs stageminder_neo4j_conf 2>/dev/null || true
        docker volume rm stageminder_maven_cache 2>/dev/null || true
        print_success "Cleanup completed!"
    else
        print_info "Cleanup cancelled."
    fi
}

# Main script logic
case "$1" in
    "start")
        case "$2" in
            "neo4j")
                start_neo4j
                ;;
            "stageminder")
                start_stageminder
                ;;
            "admin")
                start_admin
                ;;
            "dev")
                start_dev
                ;;
            *)
                start_all
                ;;
        esac
        ;;
    "stop")
        stop_all
        ;;
    "restart")
        stop_all
        sleep 2
        start_all
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs "$@"
        ;;
    "cleanup")
        cleanup
        ;;
    *)
        echo "StageMinder Docker Control Script"
        echo ""
        echo "Usage: $0 {command} [options]"
        echo ""
        echo "Commands:"
        echo "  start [service]     Start services"
        echo "    start             Start all services (production)"
        echo "    start neo4j       Start only Neo4j database"
        echo "    start stageminder Start only StageMinder app"
        echo "    start admin       Start only Admin Console"
        echo "    start dev         Start development environment"
        echo ""
        echo "  stop                Stop all services"
        echo "  restart             Restart all services"
        echo "  status              Show service status"
        echo "  logs [service]      Show logs (all or specific service)"
        echo "  cleanup             Remove all data (destructive!)"
        echo ""
        echo "Examples:"
        echo "  $0 start            # Start everything"
        echo "  $0 start dev        # Start development environment"
        echo "  $0 stop             # Stop everything"
        echo "  $0 logs backend     # Show backend logs"
        echo "  $0 status           # Show status of all services"
        ;;
esac
