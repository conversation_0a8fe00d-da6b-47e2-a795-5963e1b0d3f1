services:
  # Neo4j Database
  neo4j:
    image: neo4j:5.15-community
    container_name: stageminder-neo4j
    environment:
      - NEO4J_AUTH=neo4j/stageminder2024
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=1G
    ports:
      - "7474:7474"
      - "7687:7687"
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
    networks:
      - stageminder-network
    restart: unless-stopped

  # Backend Service - Development Mode with Hot Reload
  backend:
    image: maven:3.9-eclipse-temurin-21
    container_name: backend
    working_dir: /app
    environment:
      - SPRING_PROFILES_ACTIVE=local
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=stageminder2024
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_REGION=us-east-1
      - S3_BUCKET_NAME=test-bucket
      - SPRING_DEVTOOLS_RESTART_ENABLED=true
      - SPRING_DEVTOOLS_LIVERELOAD_ENABLED=true
    ports:
      - "8080:8080"
      - "35729:35729"
    volumes:
      - ./Build/backend:/app
      - maven_cache:/root/.m2
    command: >
      bash -c "
        mvn dependency:go-offline -B &&
        mvn spring-boot:run -Dspring-boot.run.jvmArguments='--enable-preview'
      "
    depends_on:
      - neo4j
    networks:
      - stageminder-network
    restart: unless-stopped

  # Frontend Service - 使用构建方式，参考生产环境但适配开发模式
  frontend:
    build:
      context: ./Build/frontend
      dockerfile: Dockerfile.dev  # 创建开发专用的 Dockerfile
    container_name: frontend
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8080/api/v1  # ✅ 添加 /v1
      - HOSTNAME=0.0.0.0  # 关键配置，参考生产环境
    ports:
      - "3000:3000"
    volumes:
      # 挂载源代码用于热更新
      - ./Build/frontend/src:/app/src
      - ./Build/frontend/public:/app/public
      - ./Build/frontend/messages:/app/messages
    depends_on:
      - backend
    networks:
      - stageminder-network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: stageminder-nginx
    ports:
      - "80:80"
    depends_on:
      - frontend
      - backend
    networks:
      - stageminder-network
    restart: unless-stopped
    volumes:
      - ./Scripts/Configuration/nginx/nginx-local.conf:/etc/nginx/nginx.conf:ro

volumes:
  neo4j_data:
  neo4j_logs:
  maven_cache:

networks:
  stageminder-network:
    driver: bridge 