services:
  # Admin Console Service (连接到Neo4j容器)
  admin-console:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: stageminder-admin-console
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - NEO4J_URI=bolt://stageminder-neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=stageminder2024
      - STAGEMINDER_BACKEND_URL=http://stageserver:8080
    ports:
      - "8081:8081"
    networks:
      - stageminder_stageminder-network
    restart: unless-stopped

  # Admin Frontend (Nginx)
  admin-frontend:
    image: nginx:alpine
    container_name: admin-frontend
    ports:
      - "3001:80"
    volumes:
      - ./index.html:/usr/share/nginx/html/index.html:ro
      - ./style.css:/usr/share/nginx/html/style.css:ro
      - ./script.js:/usr/share/nginx/html/script.js:ro
      - ./logo.webp:/usr/share/nginx/html/logo.webp:ro
    restart: unless-stopped

networks:
  stageminder_stageminder-network:
    external: true