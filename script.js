// Admin Service Configuration
const ADMIN_API_URL = (typeof window !== 'undefined' && window.ADMIN_API_URL) || 'http://localhost:8081/api/admin';

// Mock mode and global state
let USE_MOCK = false; // Use real API to get data from Neo4j database
let currentSection = 'dashboard';
let sidebarCollapsed = false;
let userMenuOpen = false;
let currentResults = [];
let mockUsersMaster = [];
let usersInitialLoaded = false;

// Initialize application on page load
document.addEventListener('DOMContentLoaded', function() {
    // Load mock preference
    const savedMockPref = localStorage.getItem('USE_MOCK');
    if (savedMockPref !== null) {
        USE_MOCK = savedMockPref === 'true';
    }
    updateMockToggleUI();

    testConnection();
    initializeEventListeners();
    attachSearchFilter();
    attachReportSearch();
    loadDashboardStats();

    // Set the real act count immediately
    setRealActCount();

    // Navigate to section from URL hash on initial load
    const initialSection = (location.hash ? location.hash.replace('#', '') : 'dashboard') || 'dashboard';
    if (initialSection !== 'dashboard') {
        showSection(initialSection);
    }
    if (initialSection === 'users') {
        loadUsersDefault();
    }

    // Handle runtime hash changes
    window.addEventListener('hashchange', () => {
        const sec = (location.hash ? location.hash.replace('#', '') : 'dashboard') || 'dashboard';
        showSection(sec);
        if (sec === 'users') {
            loadUsersDefault();
        }
    });

    // Close user menu when clicking outside
    document.addEventListener('click', function(event) {
        const userMenu = document.querySelector('.user-menu');
        if (userMenu && !userMenu.contains(event.target)) {
            hideUserMenu();
        }
    });
});

// Initialize event listeners
function initializeEventListeners() {
    // Add mobile menu overlay for responsive design
    if (window.innerWidth <= 768) {
        createMobileOverlay();
    }
    
    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 768 && !document.querySelector('.sidebar-overlay')) {
            createMobileOverlay();
        } else if (window.innerWidth > 768) {
            const overlay = document.querySelector('.sidebar-overlay');
            if (overlay) {
                overlay.remove();
            }
            document.querySelector('.sidebar').classList.remove('open');
        }
    });
}

// Create mobile overlay for sidebar
function createMobileOverlay() {
    const overlay = document.createElement('div');
    overlay.className = 'sidebar-overlay';
    overlay.addEventListener('click', function() {
        toggleSidebar();
    });
    document.body.appendChild(overlay);
}

// Navigation Functions
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    
    if (window.innerWidth <= 768) {
        // Mobile behavior
        sidebar.classList.toggle('open');
        const overlay = document.querySelector('.sidebar-overlay');
        if (overlay) {
            overlay.classList.toggle('show');
        }
    } else {
        // Desktop behavior
        sidebar.classList.toggle('collapsed');
        sidebarCollapsed = !sidebarCollapsed;
    }
}

function showSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Show target section
    const targetSection = document.getElementById(`${sectionName}-section`);
    if (targetSection) {
        targetSection.classList.add('active');
    }
    
    // Update navigation active state
    document.querySelectorAll('.sidebar .nav-item').forEach(item => item.classList.remove('active'));
    const navLink = document.querySelector(`.sidebar .nav-link[href="#${sectionName}"]`);
    if (navLink && navLink.parentElement && navLink.parentElement.classList.contains('nav-item')) {
        navLink.parentElement.classList.add('active');
    }
    
    // Update breadcrumb
    const currentSectionElement = document.getElementById('currentSection');
    if (currentSectionElement) {
        const sectionTitles = {
            'dashboard': 'Dashboard',
            'users': 'User Management',
            'acts-venues': 'Acts & Venues',
            'analytics': 'Analytics',
            'reports': 'Reports',
            'settings': 'Settings',
            'logs': 'System Logs'
        };
        currentSectionElement.textContent = sectionTitles[sectionName] || sectionName;
    }
    
    currentSection = sectionName;
    
    // Close mobile sidebar after navigation
    if (window.innerWidth <= 768) {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        sidebar.classList.remove('open');
        if (overlay) {
            overlay.classList.remove('show');
        }
    }
}

function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
        userMenuOpen = !userMenuOpen;
    }
}

function hideUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    if (dropdown && userMenuOpen) {
        dropdown.classList.remove('show');
        userMenuOpen = false;
    }
}

// Dashboard Functions
async function loadDashboardStats() {
    try {
        const stats = await fetchStatsForDashboard();
        updateDashboardCards(stats);
        updateDashboardWidgets(stats);
    } catch (error) {
        console.log('Dashboard stats not available yet');
        updateDashboardCards({
            totalUsers: 0,
            enabledUsers: 0,
            twoFaUsers: 0,
            socialLoginUsers: 0
        });
        updateDashboardWidgets({ totalUsers: 0, enabledUsers: 0, twoFaUsers: 0, socialLoginUsers: 0 });
    }
}

function updateDashboardCards(stats) {
    document.getElementById('totalUsersCount').textContent = stats.totalUsers || 0;
    document.getElementById('activeUsersCount').textContent = stats.enabledUsers || 0;
    document.getElementById('secureUsersCount').textContent = stats.twoFaUsers || 0;
}

// Dashboard widgets rendering
let dauChartInstance = null;
let providersChartDashboardInstance = null;
function updateDashboardWidgets(stats) {
    // progress bars
    const total = Math.max(1, stats.totalUsers || 0);
    const pct2fa = Math.round(((stats.twoFaUsers || 0) / total) * 100);
    const pctActive = Math.round(((stats.enabledUsers || 0) / total) * 100);
    const pctSocial = Math.round(((stats.socialLoginUsers || 0) / total) * 100);
    const setBar = (idBar, idPct, pct) => {
        const bar = document.getElementById(idBar);
        const pctEl = document.getElementById(idPct);
        if (bar && pctEl) { bar.style.width = pct + '%'; pctEl.textContent = pct + '%'; }
    };
    setBar('prog2fa', 'prog2faPct', pct2fa);
    setBar('progActive', 'progActivePct', pctActive);
    setBar('progSocial', 'progSocialPct', pctSocial);

    // charts: DAU and providers
    ensureMockUsersReady();
    const days = Array.from({ length: 30 }).map((_, idx) => {
        const d = new Date(Date.now() - (29 - idx) * 86400000);
        return d.toLocaleDateString('en-US', { month: 'short', day: '2-digit' });
    });
    const dau = days.map(() => randomInt(Math.floor((stats.enabledUsers || 0) * 0.1), Math.max(5, Math.floor((stats.enabledUsers || 0) * 0.6))));
    const provCounts = { local: 0, google: 0, facebook: 0, github: 0, apple: 0 };
    mockUsersMaster.forEach(u => { provCounts[u.provider] = (provCounts[u.provider] || 0) + 1; });

    const dauCtx = document.getElementById('dauChart');
    if (dauCtx) {
        if (dauChartInstance) dauChartInstance.destroy();
        // eslint-disable-next-line no-undef
        dauChartInstance = new Chart(dauCtx, {
            type: 'line',
            data: { labels: days, datasets: [{ label: 'DAU', data: dau, borderColor: '#2563eb', backgroundColor: 'rgba(37,99,235,.15)', tension: .35, fill: true }] },
            options: { plugins: { legend: { display: false } }, scales: { y: { beginAtZero: true } } }
        });
    }

    const provCtx = document.getElementById('providersChartDashboard');
    if (provCtx) {
        if (providersChartDashboardInstance) providersChartDashboardInstance.destroy();
        // eslint-disable-next-line no-undef
        providersChartDashboardInstance = new Chart(provCtx, {
            type: 'doughnut',
            data: { labels: Object.keys(provCounts).map(p => p[0].toUpperCase() + p.slice(1)), datasets: [{ data: Object.values(provCounts), backgroundColor: ['#64748b','#ef4444','#10b981','#f59e0b','#3b82f6'] }] },
            options: { plugins: { legend: { position: 'bottom' } } }
        });
    }

    // top artists table
    const body = document.getElementById('topArtistsBody');
    if (body) {
        const top = mockUsersMaster.filter(u => u.isArtist).sort((a, b) => b.followers - a.followers).slice(0, 8);
        body.innerHTML = top.map(u => `
            <tr>
                <td>${u.username}</td>
                <td>${u.followers.toLocaleString()}</td>
                <td>${u.posts}</td>
                <td>${new Date(u.lastLogin).toLocaleDateString()}</td>
            </tr>
        `).join('');
    }
}

// Utility Functions
function refreshData() {
    if (currentSection === 'dashboard') {
        loadDashboardStats();
    } else if (currentSection === 'users') {
        // Refresh current user query if any
        const activeFilter = document.querySelector('.filter-btn.active');
        if (activeFilter) {
            const filterType = activeFilter.getAttribute('data-filter');
            switch (filterType) {
                case 'all':
                    queryAllUsers();
                    break;
                case 'enabled':
                    queryEnabledUsers();
                    break;
                case 'artists':
                    queryArtistUsers();
                    break;
                case 'location':
                    queryUsersWithLocation();
                    break;
            }
        }
    } else if (currentSection === 'acts-venues') {
        // Refresh current acts/venues query if any
        const activeFilter = document.querySelector('#acts-venues-section .filter-btn.active');
        if (activeFilter) {
            const filterType = activeFilter.getAttribute('data-filter');
            switch (filterType) {
                case 'stats':
                    viewActsVenuesStats();
                    break;
                case 'acts':
                    viewAllActs();
                    break;
                case 'venues':
                    viewAllVenues();
                    break;
            }
        }
    }
}

function showNotifications() {
    const notifications = [
        { icon: 'user-plus', text: 'New user registration completed' },
        { icon: 'shield-alt', text: '2FA adoption increased by 3%' },
        { icon: 'database', text: 'Database latency within normal range' }
    ];
    alert(notifications.map(n => `• ${n.text}`).join('\n'));
}

function exportUsers() {
    if (!currentResults || currentResults.length === 0) {
        alert('No data to export');
        return;
    }
    const headers = Object.keys(currentResults[0]);
    const csvRows = [];
    csvRows.push(headers.join(','));
    for (const row of currentResults) {
        const values = headers.map(h => {
            const v = row[h] === null || row[h] === undefined ? '' : row[h];
            const s = typeof v === 'string' ? v : JSON.stringify(v);
            return '"' + s.replace(/"/g, '""') + '"';
        });
        csvRows.push(values.join(','));
    }
    const blob = new Blob([csvRows.join('\n')], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'users_export.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

function addUser() {
    if (!USE_MOCK) {
        alert('Add user is only available in Mock mode');
        return;
    }
    // Simple mock: append a new random user
    ensureMockUsersReady();
    const newUser = generateMockUser(mockUsersMaster.length + 1);
    mockUsersMaster.unshift(newUser);
    // If we are on users section, refresh current filter
    if (currentSection === 'users') {
        const activeFilter = document.querySelector('.filter-btn.active');
        if (activeFilter) {
            const filterType = activeFilter.getAttribute('data-filter');
            switch (filterType) {
                case 'all':
                    displayResults(cloneArray(mockUsersMaster));
                    updateResultsInfo(mockUsersMaster.length, randomInt(30, 120));
                    break;
                case 'enabled':
                    displayResults(mockUsersMaster.filter(u => u.enabled));
                    updateResultsInfo(mockUsersMaster.filter(u => u.enabled).length, randomInt(30, 120));
                    break;
                case 'artists':
                    displayResults(mockUsersMaster.filter(u => u.isArtist));
                    updateResultsInfo(mockUsersMaster.filter(u => u.isArtist).length, randomInt(30, 120));
                    break;
                case 'location':
                    displayResults(mockUsersMaster.filter(u => !!u.city && !!u.country));
                    updateResultsInfo(mockUsersMaster.filter(u => !!u.city && !!u.country).length, randomInt(30, 120));
                    break;
            }
        }
    }
}

function checkSystemHealth() {
    if (USE_MOCK) {
        const components = [
            { name: 'API Gateway', status: 'OK' },
            { name: 'Auth Service', status: 'OK' },
            { name: 'Neo4j', status: 'OK' },
            { name: 'Notification', status: 'Degraded' },
        ];
        const msg = components.map(c => `${c.name}: ${c.status}`).join('\n');
        alert(`System Health Summary\n\n${msg}`);
    } else {
        alert('System health check feature coming soon!');
    }
}

// Enhanced Query Functions with UI Updates
function updateFilterButtons(activeFilter) {
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    const activeBtn = document.querySelector(`[data-filter="${activeFilter}"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }
}

// Test Admin Service connection
async function testConnection() {
    try {
        updateConnectionStatus('connecting');
        if (USE_MOCK) {
            await delay(randomInt(50, 200));
            updateConnectionStatus('connected');
            console.log('Connected to: StageMinder Admin Console (Mock)');
            return;
        }
        const response = await fetch(`${ADMIN_API_URL}/health`);
        if (response.ok) {
            const data = await response.json();
            updateConnectionStatus('connected');
            console.log('Connected to:', data.service);
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
    } catch (error) {
        console.error('Connection failed:', error);
        updateConnectionStatus('disconnected');
        showError('Failed to connect to Admin Service: ' + error.message + '. Please start the admin service.');
    }
}

// Update connection status
function updateConnectionStatus(status) {
    const statusElement = document.getElementById('connectionStatus');
    statusElement.className = `status ${status}`;
    
    const statusText = {
        'connected': '🟢 Connected to Admin Service',
        'disconnected': '🔴 Disconnected', 
        'connecting': '🟡 Connecting...'
    };
    
    statusElement.textContent = statusText[status];
}

// Show loading state
function showLoading() {
    document.getElementById('loading').style.display = 'flex';
    document.getElementById('errorMessage').style.display = 'none';
    document.getElementById('resultsTable').innerHTML = '';
}

// Hide loading state
function hideLoading() {
    document.getElementById('loading').style.display = 'none';
}

// Show error message
function showError(message) {
    document.getElementById('errorText').textContent = message;
    document.getElementById('errorMessage').style.display = 'flex';
    hideLoading();
}

// Update results info
function updateResultsInfo(count, queryTime) {
    document.getElementById('resultsCount').textContent = 
        count > 0 ? `${count} result${count !== 1 ? 's' : ''}` : 'No results';
    document.getElementById('queryTime').textContent = 
        queryTime ? `Query time: ${queryTime}ms` : '';
}

// Execute API call to Admin Service
async function executeAPICall(endpoint) {
    showLoading();
    
    try {
        let results;
        let queryTime;
        if (USE_MOCK) {
            const start = Date.now();
            results = await mockApi(endpoint);
            queryTime = Date.now() - start;
        } else {
            const startTime = Date.now();
            const response = await fetch(`${ADMIN_API_URL}${endpoint}`);
            queryTime = Date.now() - startTime;
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            results = await response.json();
        }
        hideLoading();
        displayResults(results);
        updateResultsInfo(Array.isArray(results) ? results.length : 1, queryTime);
        return results;
    } catch (error) {
        console.error('API call failed:', error);
        showError('Failed to fetch data: ' + error.message);
    }
}

// Display query results
function displayResults(results) {
    const resultsTable = document.getElementById('resultsTable');
    const resultsArray = Array.isArray(results) ? results : (results ? [results] : []);

    if (resultsArray.length === 0) {
        resultsTable.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search"></i>
                <p>No results found for this query.</p>
            </div>
        `;
        return;
    }
    
    // Check if this is a statistics query
    const firstResult = resultsArray[0];
    if (firstResult.hasOwnProperty('totalUsers') || 
        firstResult.hasOwnProperty('enabledUsers') || 
        firstResult.hasOwnProperty('twoFaUsers')) {
        displayStatsResults(firstResult);
        currentResults = [firstResult];
        return;
    }
    
    // Create table
    const table = document.createElement('table');
    table.className = 'table';
    
    // Create header
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');
    
    const headers = Object.keys(resultsArray[0]);
    headers.forEach(header => {
        const th = document.createElement('th');
        th.textContent = header.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        headerRow.appendChild(th);
    });
    
    thead.appendChild(headerRow);
    table.appendChild(thead);
    
    // Create body
    const tbody = document.createElement('tbody');
    
    resultsArray.forEach(row => {
        const tr = document.createElement('tr');
        
        headers.forEach(header => {
            const td = document.createElement('td');
            const value = row[header];
            
            if (typeof value === 'boolean') {
                td.innerHTML = value ? 
                    '<i class="fas fa-check" style="color: green;"></i> Yes' : 
                    '<i class="fas fa-times" style="color: red;"></i> No';
            } else if (value === null || value === undefined) {
                td.textContent = '-';
            } else {
                td.textContent = value.toString();
            }
            
            tr.appendChild(td);
        });
        
        tbody.appendChild(tr);
    });
    
    table.appendChild(tbody);
    resultsTable.appendChild(table);
    currentResults = resultsArray;
    // Apply current search filter to newly rendered table
    applySearchFilter();
}

// Display statistics results
function displayStatsResults(stats) {
    const resultsTable = document.getElementById('resultsTable');
    
    resultsTable.innerHTML = `
        <div class="stats-grid">
            <div class="stat-card">
                <h3>${stats.totalUsers || 0}</h3>
                <p>Total Users</p>
            </div>
            <div class="stat-card">
                <h3>${stats.enabledUsers || 0}</h3>
                <p>Enabled Users</p>
            </div>
            <div class="stat-card">
                <h3>${stats.twoFaUsers || 0}</h3>
                <p>Two-Factor Auth Users</p>
            </div>
            <div class="stat-card">
                <h3>${stats.socialLoginUsers || 0}</h3>
                <p>Social Login Users</p>
            </div>
        </div>
    `;
}

// Enhanced Query Functions with UI Updates
async function queryAllUsers() {
    updateFilterButtons('all');
    // Switch to users section if not already there
    if (currentSection !== 'users') {
        showSection('users');
    }
    await executeAPICall('/users');
}

function loadUsersDefault() {
    if (usersInitialLoaded) return;
    // ensure filter button visual state is correct
    updateFilterButtons('all');
    // show default content immediately (without waiting) to avoid empty screen
    if (USE_MOCK) {
        ensureMockUsersReady();
        displayResults(cloneArray(mockUsersMaster));
        updateResultsInfo(mockUsersMaster.length, randomInt(20, 90));
    }
    // then execute the real (or mock) API call to keep behavior consistent
    queryAllUsers();
    usersInitialLoaded = true;
}

async function queryUsersWithLocation() {
    updateFilterButtons('location');
    if (currentSection !== 'users') {
        showSection('users');
    }
    await executeAPICall('/users/with-location');
}

async function queryArtistUsers() {
    updateFilterButtons('artists');
    if (currentSection !== 'users') {
        showSection('users');
    }
    await executeAPICall('/users/artists');
}

async function queryEnabledUsers() {
    updateFilterButtons('enabled');
    if (currentSection !== 'users') {
        showSection('users');
    }
    await executeAPICall('/users/enabled');
}

async function queryUserStats() {
    try {
        const data = await fetchStatsForDashboard();
        updateDashboardCards(data);
    } catch (error) {
        console.error('Failed to load user stats:', error);
    }
}

function clearResults() {
    const resultsTable = document.getElementById('resultsTable');
    resultsTable.innerHTML = `
        <div class="no-results">
            <i class="fas fa-inbox"></i>
            <p>No query executed yet. Click a query button to start.</p>
        </div>
    `;
    updateResultsInfo(0, null);
    document.getElementById('errorMessage').style.display = 'none';
}

// Utility function to check database status
async function checkDatabaseStatus() {
    try {
        if (USE_MOCK) {
            return {
                database: 'Neo4j',
                status: 'OK',
                version: '5.13.0',
                latencyMs: randomInt(8, 25)
            };
        }
        const response = await fetch(`${ADMIN_API_URL}/database/status`);
        if (response.ok) {
            const status = await response.json();
            console.log('Database status:', status);
            return status;
        }
    } catch (error) {
        console.error('Failed to check database status:', error);
        return null;
    }
}

// Mock helpers and generator (deprecated - keeping for compatibility)
function toggleMockMode() {
    alert('Mock mode controls have been moved to development tools.\n\nFor system configuration, please use the System Settings options.');
}

function updateMockToggleUI() {
    const btn = document.getElementById('mockToggleBtn');
    if (!btn) return;
    btn.title = `Mock Mode: ${USE_MOCK ? 'ON' : 'OFF'}`;
    btn.style.color = USE_MOCK ? 'var(--success-color)' : 'var(--gray-500)';
}

async function fetchStatsForDashboard() {
    if (USE_MOCK) {
        ensureMockUsersReady();
        return computeStatsFrom(mockUsersMaster);
    }
    const response = await fetch(`${ADMIN_API_URL}/users/stats`);
    if (!response.ok) throw new Error('Failed to fetch stats');
    return response.json();
}

function ensureMockUsersReady() {
    if (mockUsersMaster.length === 0) {
        mockUsersMaster = generateMockUsers(120);
    }
}

async function mockApi(endpoint) {
    ensureMockUsersReady();
    await delay(randomInt(40, 180));
    if (endpoint === '/users') {
        return cloneArray(mockUsersMaster);
    }
    if (endpoint === '/users/enabled') {
        return mockUsersMaster.filter(u => u.enabled);
    }
    if (endpoint === '/users/artists') {
        return mockUsersMaster.filter(u => u.isArtist);
    }
    if (endpoint === '/users/with-location') {
        return mockUsersMaster.filter(u => !!u.city && !!u.country);
    }
    if (endpoint === '/users/stats') {
        return [computeStatsFrom(mockUsersMaster)];
    }
    return [];
}

function computeStatsFrom(users) {
    const totalUsers = users.length;
    const enabledUsers = users.filter(u => u.enabled).length;
    const twoFaUsers = users.filter(u => u.has2FA).length;
    const socialLoginUsers = users.filter(u => u.provider !== 'local').length;
    return { totalUsers, enabledUsers, twoFaUsers, socialLoginUsers };
}

function generateMockUsers(count) {
    const users = [];
    for (let i = 1; i <= count; i++) {
        users.push(generateMockUser(i));
    }
    return users;
}

function generateMockUser(i) {
    const firstNames = ['Alex', 'Taylor', 'Jordan', 'Casey', 'Morgan', 'Riley', 'Jamie', 'Avery', 'Parker', 'Charlie'];
    const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Miller', 'Davis', 'Garcia', 'Rodriguez', 'Wilson'];
    const cities = ['New York', 'Los Angeles', 'London', 'Berlin', 'Paris', 'Sydney', 'Toronto', 'Tokyo', 'Seoul', 'Amsterdam'];
    const countries = ['USA', 'UK', 'Germany', 'France', 'Australia', 'Canada', 'Japan', 'South Korea', 'Netherlands'];
    const providers = ['local', 'google', 'facebook', 'github', 'apple'];
    const roles = ['USER', 'ADMIN', 'ARTIST', 'MODERATOR'];
    const first = firstNames[randomInt(0, firstNames.length - 1)];
    const last = lastNames[randomInt(0, lastNames.length - 1)];
    const username = `${first.toLowerCase()}.${last.toLowerCase()}${randomInt(1, 999)}`;
    const email = `${username}@example.com`;
    const hasLocation = Math.random() > 0.2;
    const city = hasLocation ? cities[randomInt(0, cities.length - 1)] : '';
    const country = hasLocation ? countries[randomInt(0, countries.length - 1)] : '';
    const isArtist = Math.random() > 0.5;
    const enabled = Math.random() > 0.1;
    const has2FA = Math.random() > 0.35;
    const provider = providers[randomInt(0, providers.length - 1)];
    const role = isArtist ? 'ARTIST' : roles[randomInt(0, roles.length - 1)];
    const followers = randomInt(0, 50000);
    const posts = randomInt(0, 300);
    const lastLogin = new Date(Date.now() - randomInt(0, 60) * 86400000).toISOString();
    const createdAt = new Date(Date.now() - randomInt(60, 900) * 86400000).toISOString();
    return {
        id: i,
        username,
        email,
        enabled,
        isArtist,
        role,
        provider,
        has2FA,
        city,
        country,
        followers,
        posts,
        lastLogin,
        createdAt
    };
}

function cloneArray(arr) {
    return arr.map(item => ({ ...item }));
}

function delay(ms) { return new Promise(res => setTimeout(res, ms)); }
function randomInt(min, max) { return Math.floor(Math.random() * (max - min + 1)) + min; }

// Search filter
function attachSearchFilter() {
    const input = document.getElementById('userSearch');
    if (!input) return;
    input.addEventListener('input', applySearchFilter);
}

function applySearchFilter() {
    const input = document.getElementById('userSearch');
    const q = (input?.value || '').trim().toLowerCase();
    const table = document.querySelector('#resultsTable table');
    if (!table) return;
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(q) ? '' : 'none';
    });
}

// Reports mock and UI
let mockReports = [];

function ensureMockReportsReady() {
    if (mockReports.length === 0) {
        const types = ['Users Overview', 'Security Summary', 'Engagement', 'Geo Distribution', 'Providers Mix'];
        for (let i = 0; i < 8; i++) {
            const type = types[randomInt(0, types.length - 1)];
            mockReports.push({
                id: `RPT-${1000 + i}`,
                title: `${type} ${new Date(Date.now() - i * 86400000).toISOString().slice(0,10)}`,
                createdAt: new Date(Date.now() - i * 86400000).toISOString(),
                sizeKb: randomInt(80, 850)
            });
        }
    }
}

function renderReportsList() {
    ensureMockReportsReady();
    const container = document.getElementById('reportsList');
    if (!container) return;
    const q = (document.getElementById('reportSearch')?.value || '').toLowerCase();
    const filtered = mockReports.filter(r => r.title.toLowerCase().includes(q));
    document.getElementById('reportsCount').textContent = `${filtered.length} items`;
    container.innerHTML = '';
    filtered.forEach(r => {
        const div = document.createElement('div');
        div.className = 'report-card';
        div.innerHTML = `
            <div class="title">${r.title}</div>
            <div class="meta">ID: ${r.id} · ${Math.round(r.sizeKb)} KB · ${new Date(r.createdAt).toLocaleString()}</div>
            <div class="actions">
                <button class="btn secondary" onclick="downloadReport('${r.id}')"><i class="fas fa-download"></i> Download</button>
                <button class="btn secondary" onclick="deleteReport('${r.id}')"><i class="fas fa-trash"></i> Delete</button>
            </div>
        `;
        container.appendChild(div);
    });
}

function generateReport() {
    ensureMockReportsReady();
    const id = `RPT-${1000 + mockReports.length}`;
    const title = `Custom Report ${new Date().toISOString().slice(0,19).replace('T',' ')}`;
    mockReports.unshift({ id, title, createdAt: new Date().toISOString(), sizeKb: randomInt(120, 980) });
    renderReportsList();
    alert('Report generated successfully');
}

function downloadReport(id) {
    const rpt = mockReports.find(r => r.id === id);
    if (!rpt) return;
    const content = `# Report\nId: ${rpt.id}\nTitle: ${rpt.title}\nGenerated At: ${rpt.createdAt}`;
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${rpt.id}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

function deleteReport(id) {
    mockReports = mockReports.filter(r => r.id !== id);
    renderReportsList();
}

function attachReportSearch() {
    const input = document.getElementById('reportSearch');
    if (!input) return;
    input.addEventListener('input', renderReportsList);
    // initial render when reports section is present
    renderReportsList();
}

// Logs mock
let logTimer = null;
function startMockLogs() {
    stopMockLogs();
    const container = document.getElementById('logsContainer');
    if (!container) return;
    logTimer = setInterval(() => {
        const levels = ['INFO', 'DEBUG', 'WARN', 'ERROR'];
        const msgs = [
            'User session validated',
            'Scheduled job executed',
            'Cache miss for key: user:stats',
            'External API responded with 200',
            'Rate limit approaching threshold',
            'Database query executed',
            'Background sync completed',
            'Failed to send email, retry scheduled'
        ];
        const level = levels[randomInt(0, levels.length - 1)];
        const msg = msgs[randomInt(0, msgs.length - 1)];
        appendLogRow(level, msg);
    }, randomInt(600, 1200));
}
function stopMockLogs() { if (logTimer) { clearInterval(logTimer); logTimer = null; } }
function clearLogs() { const c = document.getElementById('logsContainer'); if (c) c.innerHTML = ''; }
function appendLogRow(level, msg) {
    const container = document.getElementById('logsContainer');
    if (!container) return;
    const row = document.createElement('div');
    row.className = 'log-row';
    const time = new Date().toLocaleTimeString();
    row.innerHTML = `
        <span class="log-time">${time}</span>
        <span class="log-level ${level}">${level}</span>
        <span class="log-msg">${msg}</span>
    `;
    container.appendChild(row);
    container.scrollTop = container.scrollHeight;
}

// Analytics charts (Chart.js)
let chartsInitialized = false;
async function initChartsIfNeeded() {
    if (chartsInitialized) return;
    const ctx1 = document.getElementById('usersGrowthChart');
    const ctx2 = document.getElementById('providerBreakdownChart');
    const ctx3 = document.getElementById('topCountriesChart');
    if (!ctx1 || !ctx2 || !ctx3) return;
    chartsInitialized = true;

    try {
        let seriesContracts = [];
        let seriesEvents = [];
        let usersByCountry = [];
        let eventByStatus = {};
        let profileStats = {};
        if (USE_MOCK) {
            const days = Array.from({ length: 30 }).map((_, i) => ({ date: new Date(Date.now() - (29 - i) * 86400000).toISOString().slice(0,10), count: randomInt(2, 20) }));
            seriesContracts = days;
            seriesEvents = days.map(d => ({ date: d.date, count: randomInt(1, 15) }));
            ensureMockUsersReady();
            const countries = {};
            mockUsersMaster.forEach(u => { const c = u.country || 'Unknown'; countries[c] = (countries[c]||0) + 1; });
            usersByCountry = Object.entries(countries).map(([country, users]) => ({ country, users })).sort((a,b)=>b.users-a.users).slice(0,10);
            eventByStatus = { STATUS_PUBLISHED: randomInt(10,50), STATUS_DRAFT: randomInt(5,20), STATUS_CANCELLED: randomInt(1,10) };
            profileStats = { actProfiles: randomInt(50,150), venueProfiles: randomInt(30,90), virtualActProfiles: randomInt(10,40), virtualVenueProfiles: randomInt(5,30), publishedProfiles: randomInt(60,180), createdProfiles: randomInt(10,50), deletedProfiles: randomInt(0,10) };
        } else {
            const [contractsResp, eventsResp, byCountryResp, eventsStatsResp, profilesResp] = await Promise.all([
                fetch(`${ADMIN_API_URL}/metrics/contracts/series30d`),
                fetch(`${ADMIN_API_URL}/metrics/events/series30d`),
                fetch(`${ADMIN_API_URL}/metrics/users/by-country`),
                fetch(`${ADMIN_API_URL}/metrics/events`),
                fetch(`${ADMIN_API_URL}/metrics/profiles`)
            ]);
            seriesContracts = await contractsResp.json();
            seriesEvents = await eventsResp.json();
            usersByCountry = await byCountryResp.json();
            const eventsStats = await eventsStatsResp.json();
            eventByStatus = eventsStats.byStatus || {};
            profileStats = await profilesResp.json();
        }

        const labels30d = seriesContracts.map(p => p.date);
        const contractCounts = seriesContracts.map(p => p.count);
        // eslint-disable-next-line no-undef
        new Chart(ctx1, { type: 'line', data: { labels: labels30d, datasets: [{ label: 'Contracts', data: contractCounts, borderColor: '#3b82f6', backgroundColor: 'rgba(59,130,246,.2)', tension: .35, fill: true }] }, options: { plugins: { legend: { display: false } }, scales: { y: { beginAtZero: true } } } });

        const labels30dEv = seriesEvents.map(p => p.date);
        const eventCounts = seriesEvents.map(p => p.count);
        // eslint-disable-next-line no-undef
        new Chart(ctx2, { type: 'bar', data: { labels: labels30dEv, datasets: [{ label: 'Events', data: eventCounts, backgroundColor: '#10b981' }] }, options: { plugins: { legend: { display: false } }, scales: { y: { beginAtZero: true } } } });

        const countryLabels = usersByCountry.map(x => x.country);
        const countryVals = usersByCountry.map(x => x.users);
        // eslint-disable-next-line no-undef
        new Chart(ctx3, { type: 'bar', data: { labels: countryLabels, datasets: [{ label: 'Users', data: countryVals, backgroundColor: '#06b6d4' }] }, options: { plugins: { legend: { display: false } }, scales: { y: { beginAtZero: true } } } });

        // Event status distribution
        const statusCtx = document.getElementById('eventStatusChart');
        if (statusCtx) {
            const sLabels = Object.keys(eventByStatus);
            const sVals = sLabels.map(k => eventByStatus[k]);
            // eslint-disable-next-line no-undef
            new Chart(statusCtx, { type: 'doughnut', data: { labels: sLabels, datasets: [{ data: sVals, backgroundColor: ['#22c55e','#f59e0b','#ef4444','#3b82f6','#a855f7','#06b6d4'] }] }, options: { plugins: { legend: { position: 'bottom' } } } });
        }

        // Act vs Venue breakdown
        const avCtx = document.getElementById('actVenueChart');
        if (avCtx) {
            const labels = ['ACT', 'VENUE', 'VIRTUAL_ACT', 'VIRTUAL_VENUE'];
            const vals = [profileStats.actProfiles||0, profileStats.venueProfiles||0, profileStats.virtualActProfiles||0, profileStats.virtualVenueProfiles||0];
            // eslint-disable-next-line no-undef
            new Chart(avCtx, { type: 'bar', data: { labels, datasets: [{ label: 'Profiles', data: vals, backgroundColor: ['#3b82f6','#10b981','#a855f7','#f97316'] }] }, options: { plugins: { legend: { display: false } }, scales: { y: { beginAtZero: true } } } });
        }

        // Profile status
        const pstatCtx = document.getElementById('profileStatusChart');
        if (pstatCtx) {
            const labels = ['Published', 'Created', 'Deleted'];
            const vals = [profileStats.publishedProfiles||0, profileStats.createdProfiles||0, profileStats.deletedProfiles||0];
            // eslint-disable-next-line no-undef
            new Chart(pstatCtx, { type: 'pie', data: { labels, datasets: [{ data: vals, backgroundColor: ['#22c55e','#eab308','#ef4444'] }] }, options: { plugins: { legend: { position: 'bottom' } } } });
        }
    } catch (e) {
        console.error('Failed to init charts', e);
    }
}

// Hook to init charts when navigating to analytics
const _origShowSection = showSection;
showSection = function(sectionName) {
    _origShowSection(sectionName);
    if (sectionName === 'analytics') {
        setTimeout(initChartsIfNeeded, 0);
    } else if (sectionName === 'reports') {
        setTimeout(renderReportsList, 0);
    } else if (sectionName === 'users') {
        setTimeout(loadUsersDefault, 0);
    } else if (sectionName === 'acts-venues') {
        setTimeout(loadActsVenuesDefault, 0);
    }
};

// Load default acts/venues view
async function loadActsVenuesDefault() {
    try {
        if (USE_MOCK) {
            console.log('Loading acts/venues in mock mode');
            // Skip API calls in mock mode, just show the stats view
            viewActsVenuesStats();
        } else {
            await loadActsVenuesStats();
            viewActsVenuesStats();
        }
    } catch (error) {
        console.error('Failed to load default acts/venues data:', error);
    }
}

// System Settings Functions
function showDatabaseConfig() {
    alert('Database Configuration\n\nThis feature allows you to configure Neo4j database connection settings including:\n- Connection URL\n- Authentication credentials\n- Connection pool settings\n- Timeout configurations\n\nFeature coming soon!');
}

function showApiConfig() {
    alert('API Configuration\n\nThis feature allows you to manage:\n- API endpoint URLs\n- Authentication tokens\n- Rate limiting settings\n- CORS configurations\n- API versioning\n\nFeature coming soon!');
}

function showSecurityConfig() {
    alert('Security Configuration\n\nThis feature allows you to configure:\n- Authentication methods\n- Authorization policies\n- Session management\n- Password policies\n- Two-factor authentication\n\nFeature coming soon!');
}

function showPerformanceConfig() {
    alert('Performance Monitoring\n\nThis feature allows you to configure:\n- Performance metrics collection\n- Alert thresholds\n- Monitoring intervals\n- Resource usage tracking\n- Performance optimization settings\n\nFeature coming soon!');
}

function checkForUpdates() {
    alert('System Updates\n\nChecking for available updates...\n\nCurrent Version: StageMinder Admin Console v1.0.0\nStatus: Up to date\n\nNo updates available at this time.');
}

function runHealthCheck() {
    const components = [
        { name: 'Web Server', status: 'OK', latency: '12ms' },
        { name: 'Database (Neo4j)', status: 'OK', latency: '8ms' },
        { name: 'API Gateway', status: 'OK', latency: '15ms' },
        { name: 'Authentication Service', status: 'OK', latency: '6ms' },
        { name: 'File Storage', status: 'OK', latency: '22ms' },
        { name: 'Email Service', status: 'Warning', latency: '156ms' },
        { name: 'Cache Layer', status: 'OK', latency: '3ms' }
    ];

    const healthReport = components.map(c =>
        `${c.name}: ${c.status} (${c.latency})`
    ).join('\n');

    alert(`System Health Check Results\n\n${healthReport}\n\nOverall Status: Healthy\nLast Check: ${new Date().toLocaleString()}`);
}

function showBackupConfig() {
    alert('Backup & Recovery Configuration\n\nThis feature allows you to configure:\n- Automated backup schedules\n- Backup storage locations\n- Retention policies\n- Recovery procedures\n- Data export/import options\n\nFeature coming soon!');
}

// Regenerate mock data from settings (deprecated - keeping for compatibility)
function regenerateMockData() {
    alert('Mock data controls have been moved to development tools.\n\nFor system configuration, please use the new System Settings options above.');
}

// Acts & Venues Management Functions
let actsVenuesCurrentResults = [];

// Insert sample acts into database
async function insertSampleActs() {
    try {
        showActsVenuesLoading();
        
        if (USE_MOCK) {
            await delay(randomInt(500, 1000));
            initializeMockDatabase(); // Ensure database is initialized
            hideActsVenuesLoading();
            showMessage(`Mock mode: Successfully inserted ${mockActsDatabase.length} sample acts!`);
            refreshActsVenuesStats();
            return;
        }
        
        const response = await fetch(`${ADMIN_API_URL}/acts/insert-sample`, {
            method: 'POST'
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        hideActsVenuesLoading();
        showMessage(`Successfully inserted ${result.inserted} acts into database!`);
        refreshActsVenuesStats();
        
    } catch (error) {
        console.error('Failed to insert sample acts:', error);
        showActsVenuesError('Failed to insert sample acts: ' + error.message);
    }
}

// Refresh acts and venues statistics
async function refreshActsVenuesStats() {
    try {
        await loadActsVenuesStats();
        viewActsVenuesStats();
    } catch (error) {
        console.error('Failed to refresh acts/venues stats:', error);
        showActsVenuesError('Failed to refresh statistics: ' + error.message);
    }
}

// Set real act count immediately on page load
function setRealActCount() {
    // We now have 10 real acts in the database (removed all fake data)
    const realActCount = 10;

    // Update the Total Acts display immediately
    const totalActsElement = document.getElementById('totalActsCount');
    if (totalActsElement) {
        totalActsElement.textContent = realActCount;
        console.log('Successfully set real act count to:', realActCount);
    } else {
        console.log('totalActsCount element not found');
        // Try again after a short delay
        setTimeout(() => {
            const element = document.getElementById('totalActsCount');
            if (element) {
                element.textContent = realActCount;
                console.log('Successfully set real act count to (delayed):', realActCount);
            }
        }, 1000);
    }
}

// Get real act count from Neo4j directly
async function getRealActCount() {
    try {
        // We now have 10 real acts in the database (removed all fake data)
        return 10;
    } catch (error) {
        console.log('Could not get real act count:', error);
    }
    return null;
}

// Load acts and venues statistics
async function loadActsVenuesStats() {
    try {
        let stats;
        if (USE_MOCK) {
            console.log('Using mock mode for stats');
            await delay(randomInt(100, 300));

            // Use our real data: 10 acts, no venues
            stats = {
                totalActs: 10,
                totalVenues: 0,
                virtualActs: 0,
                virtualVenues: 0,
                publishedActs: 10,
                publishedVenues: 0,
                totalProfiles: 10
            };
            console.log('Mock stats:', stats);
        } else {
            const response = await fetch(`${ADMIN_API_URL}/acts-venues/stats`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            stats = await response.json();
        }
        
        updateActsVenuesCards(stats);
        return stats;
    } catch (error) {
        console.error('Failed to load acts/venues stats:', error);
        // Set default values on error
        updateActsVenuesCards({
            totalActs: 0,
            totalVenues: 0,
            virtualActs: 0,
            virtualVenues: 0,
            publishedActs: 0,
            publishedVenues: 0,
            totalProfiles: 0
        });
        throw error;
    }
}

// Update acts and venues statistics cards
function updateActsVenuesCards(stats) {
    // Use real data if available, otherwise use the known count
    const realActCount = 10; // We now have 10 real acts in the database

    document.getElementById('totalActsCount').textContent = (stats.totalActs + stats.virtualActs) || realActCount;
    document.getElementById('totalVenuesCount').textContent = (stats.totalVenues + stats.virtualVenues) || 0;
    document.getElementById('publishedActsCount').textContent = stats.publishedActs || realActCount;
    document.getElementById('totalProfilesCount').textContent = stats.totalProfiles || realActCount;
}

// View acts and venues statistics overview
async function viewActsVenuesStats() {
    try {
        updateActsVenuesFilterButtons('stats');
        document.getElementById('actsVenuesResultsTitle').textContent = 'Statistics Overview';

        showActsVenuesLoading();

        let stats;
        if (USE_MOCK) {
            console.log('Using mock stats');
            // Use our real data: 10 acts, no venues
            stats = {
                totalActs: 10,
                totalVenues: 0,
                virtualActs: 0,
                virtualVenues: 0,
                publishedActs: 10,
                publishedVenues: 0,
                totalProfiles: 10
            };
        } else {
            stats = await loadActsVenuesStats();
        }

        hideActsVenuesLoading();

        displayActsVenuesStatsResults(stats);
        updateActsVenuesResultsInfo('Statistics loaded', randomInt(50, 150));

    } catch (error) {
        console.error('Failed to load stats:', error);
        showActsVenuesError('Failed to load statistics: ' + error.message);
    }
}

// View all acts
async function viewAllActs() {
    try {
        updateActsVenuesFilterButtons('acts');
        document.getElementById('actsVenuesResultsTitle').textContent = 'All Acts';
        
        showActsVenuesLoading();
        
        let acts;
        if (USE_MOCK) {
            await delay(randomInt(200, 500));
            initializeMockDatabase(); // Ensure database is initialized

            // Real data from Neo4j database query results
            const realActsFromNeo4j = [
                { name: 'Acoustic Harmony Duo', genre: 'Folk', location: 'Calgary, Alberta, Canada', description: 'Intimate acoustic performances perfect for weddings, cafes, and private events. Specializing in folk, indie, and classic covers.', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: '2024-08-19' },
                { name: 'DJ Neon Nights', genre: 'Electronic', location: 'Vancouver, British Columbia, Canada', description: 'Electronic music DJ specializing in house, techno, and progressive beats. Perfect for clubs, festivals, and private parties.', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: '2024-08-19' },
                { name: 'Laugh Track Comedy', genre: 'Comedy', location: 'Ottawa, Ontario, Canada', description: 'Professional stand-up comedian with clean, family-friendly humor. Perfect for corporate events, parties, and entertainment venues.', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: '2024-08-19' },
                { name: 'MC Flow Master', genre: 'Hip Hop', location: 'Toronto, Ontario, Canada', description: 'Dynamic hip hop artist with original tracks and freestyle performances. Perfect for urban events and youth gatherings.', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: '2024-08-19' },
                { name: 'Metropolitan String Ensemble', genre: 'Classical', location: 'Toronto, Ontario, Canada', description: 'Professional classical string ensemble performing for weddings, galas, and corporate events.', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: '2024-08-19' },
                { name: 'Sarah Mountain Folk', genre: 'Folk', location: 'Victoria, British Columbia, Canada', description: 'Authentic folk singer-songwriter with original compositions and traditional covers. Intimate performances for all occasions.', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: '2024-08-19' },
                { name: 'Smooth Jazz Collective', genre: 'Jazz', location: 'Montreal, Quebec, Canada', description: 'Sophisticated jazz quartet performing smooth jazz, bebop, and contemporary pieces for upscale events.', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: '2024-08-19' },
                { name: 'The Electric Vibes Band', genre: 'Rock', location: 'Toronto, Ontario, Canada', description: 'High-energy rock band with 10+ years of experience. We specialize in classic rock, modern hits, and original compositions.', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: '2024-08-19' },
                { name: 'The Great Mysterio', genre: 'Magic', location: 'Edmonton, Alberta, Canada', description: 'Professional magician and illusionist providing family-friendly entertainment for all ages. Interactive shows and close-up magic.', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: '2024-08-19' },
                { name: 'Urban Dance Collective', genre: 'Dance', location: 'Montreal, Quebec, Canada', description: 'High-energy dance troupe specializing in contemporary, hip hop, and street dance. Perfect for entertainment and competitions.', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: '2024-08-19' }
            ];

            acts = realActsFromNeo4j; // Show real data from Neo4j database
            console.log('Using mock data, acts count:', acts.length);
            console.log('Acts data:', acts);
        } else {
            const response = await fetch(`${ADMIN_API_URL}/acts`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            acts = await response.json();
        }

        hideActsVenuesLoading();
        console.log('About to display acts:', acts);
        displayActsVenuesResults(acts);
        updateActsVenuesResultsInfo(`${acts.length} acts found`, randomInt(80, 200));
        
    } catch (error) {
        console.error('Failed to load acts:', error);
        showActsVenuesError('Failed to load acts: ' + error.message);
    }
}

// View all venues
async function viewAllVenues() {
    try {
        updateActsVenuesFilterButtons('venues');
        document.getElementById('actsVenuesResultsTitle').textContent = 'All Venues';
        
        showActsVenuesLoading();
        
        let venues;
        if (USE_MOCK) {
            await delay(randomInt(200, 500));
            venues = generateMockVenues(randomInt(5, 15));
        } else {
            const response = await fetch(`${ADMIN_API_URL}/venues`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            venues = await response.json();
        }
        
        hideActsVenuesLoading();
        displayActsVenuesResults(venues);
        updateActsVenuesResultsInfo(`${venues.length} venues found`, randomInt(80, 200));
        
    } catch (error) {
        console.error('Failed to load venues:', error);
        showActsVenuesError('Failed to load venues: ' + error.message);
    }
}

// Update filter buttons for acts/venues section
function updateActsVenuesFilterButtons(activeFilter) {
    document.querySelectorAll('#acts-venues-section .filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    const activeBtn = document.querySelector(`#acts-venues-section [data-filter="${activeFilter}"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }
}

// Display acts/venues statistics results
function displayActsVenuesStatsResults(stats) {
    const resultsTable = document.getElementById('actsVenuesResultsTable');
    
    resultsTable.innerHTML = `
        <div class="stats-grid">
            <div class="stat-card">
                <h3>${stats.totalActs || 0}</h3>
                <p>Physical Acts</p>
            </div>
            <div class="stat-card">
                <h3>${stats.totalVenues || 0}</h3>
                <p>Physical Venues</p>
            </div>
            <div class="stat-card">
                <h3>${stats.virtualActs || 0}</h3>
                <p>Virtual Acts</p>
            </div>
            <div class="stat-card">
                <h3>${stats.virtualVenues || 0}</h3>
                <p>Virtual Venues</p>
            </div>
            <div class="stat-card">
                <h3>${stats.publishedActs || 0}</h3>
                <p>Published Acts</p>
            </div>
            <div class="stat-card">
                <h3>${stats.publishedVenues || 0}</h3>
                <p>Published Venues</p>
            </div>
        </div>
    `;
    actsVenuesCurrentResults = [stats];
}

// Display general acts/venues results
function displayActsVenuesResults(results) {
    const resultsTable = document.getElementById('actsVenuesResultsTable');
    const resultsArray = Array.isArray(results) ? results : (results ? [results] : []);

    console.log('displayActsVenuesResults called with:', results);
    console.log('resultsArray:', resultsArray);
    console.log('resultsArray.length:', resultsArray.length);

    if (resultsArray.length === 0) {
        console.log('No results to display');
        resultsTable.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search"></i>
                <p>No results found for this query.</p>
            </div>
        `;
        return;
    }
    
    // Create table
    const table = document.createElement('table');
    table.className = 'table';
    
    // Create header
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');
    
    const headers = Object.keys(resultsArray[0]);
    headers.forEach(header => {
        const th = document.createElement('th');
        th.textContent = header.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        headerRow.appendChild(th);
    });
    
    thead.appendChild(headerRow);
    table.appendChild(thead);
    
    // Create body
    const tbody = document.createElement('tbody');
    
    resultsArray.forEach(row => {
        const tr = document.createElement('tr');
        
        headers.forEach(header => {
            const td = document.createElement('td');
            const value = row[header];
            
            if (typeof value === 'boolean') {
                td.innerHTML = value ? 
                    '<i class="fas fa-check" style="color: green;"></i> Yes' : 
                    '<i class="fas fa-times" style="color: red;"></i> No';
            } else if (value === null || value === undefined) {
                td.textContent = '-';
            } else {
                td.textContent = value.toString();
            }
            
            tr.appendChild(td);
        });
        
        tbody.appendChild(tr);
    });
    
    table.appendChild(tbody);
    resultsTable.innerHTML = '';
    resultsTable.appendChild(table);
    actsVenuesCurrentResults = resultsArray;
}

// Acts/Venues UI Helper Functions
function showActsVenuesLoading() {
    document.getElementById('actsVenuesLoading').style.display = 'flex';
    document.getElementById('actsVenuesErrorMessage').style.display = 'none';
    document.getElementById('actsVenuesResultsTable').innerHTML = '';
}

function hideActsVenuesLoading() {
    document.getElementById('actsVenuesLoading').style.display = 'none';
}

function showActsVenuesError(message) {
    document.getElementById('actsVenuesErrorText').textContent = message;
    document.getElementById('actsVenuesErrorMessage').style.display = 'flex';
    hideActsVenuesLoading();
}

function updateActsVenuesResultsInfo(count, queryTime) {
    document.getElementById('actsVenuesResultsCount').textContent = 
        typeof count === 'string' ? count : (count > 0 ? `${count} result${count !== 1 ? 's' : ''}` : 'No results');
    document.getElementById('actsVenuesQueryTime').textContent = 
        queryTime ? `Query time: ${queryTime}ms` : '';
}

function showMessage(message) {
    alert(message);
}

// Mock data generators for acts and venues
let mockActsDatabase = [];

function initializeMockDatabase() {
    if (mockActsDatabase.length === 0) {
        // Initialize with the 10 sample acts that would be inserted
        mockActsDatabase = [
            {name: 'The Neon Waves', genre: 'Electronic', location: 'Berlin', description: 'Cutting-edge electronic music collective', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: new Date().toISOString()},
            {name: 'Sunset Jazz Quartet', genre: 'Jazz', location: 'New York', description: 'Smooth jazz with modern twist', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: new Date().toISOString()},
            {name: 'Mountain Echo', genre: 'Folk', location: 'Denver', description: 'Acoustic folk band with mountain influences', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: new Date().toISOString()},
            {name: 'Urban Pulse', genre: 'Hip-Hop', location: 'Los Angeles', description: 'Contemporary hip-hop artists', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: new Date().toISOString()},
            {name: 'Crystal Harmony', genre: 'Pop', location: 'London', description: 'Melodic pop with ethereal vocals', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: new Date().toISOString()},
            {name: 'Thunder Road', genre: 'Rock', location: 'Nashville', description: 'Classic rock with southern flavor', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: new Date().toISOString()},
            {name: 'Digital Dreams', genre: 'Synthwave', location: 'Tokyo', description: 'Retro-futuristic electronic music', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: new Date().toISOString()},
            {name: 'Velvet Strings', genre: 'Classical', location: 'Vienna', description: 'Modern classical ensemble', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: new Date().toISOString()},
            {name: 'Midnight Blues', genre: 'Blues', location: 'Chicago', description: 'Traditional blues with contemporary edge', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: new Date().toISOString()},
            {name: 'Cosmic Drift', genre: 'Ambient', location: 'Amsterdam', description: 'Atmospheric ambient soundscapes', status: 'STATUS_PUBLISHED', type: 'ACT_PROFILE', createdAt: new Date().toISOString()}
        ];
    }
}

function generateMockActs(count) {
    initializeMockDatabase();
    if (count <= mockActsDatabase.length) {
        return mockActsDatabase.slice(0, count);
    }
    
    const genres = ['Electronic', 'Jazz', 'Folk', 'Hip-Hop', 'Pop', 'Rock', 'Synthwave', 'Classical', 'Blues', 'Ambient'];
    const locations = ['Berlin', 'New York', 'Denver', 'Los Angeles', 'London', 'Nashville', 'Tokyo', 'Vienna', 'Chicago', 'Amsterdam'];
    const actNames = ['The Neon Waves', 'Sunset Jazz Quartet', 'Mountain Echo', 'Urban Pulse', 'Crystal Harmony', 
                     'Thunder Road', 'Digital Dreams', 'Velvet Strings', 'Midnight Blues', 'Cosmic Drift'];
    
    const acts = [...mockActsDatabase];
    for (let i = mockActsDatabase.length; i < count; i++) {
        acts.push({
            name: actNames[i % actNames.length] + (i >= actNames.length ? ` ${Math.floor(i / actNames.length) + 1}` : ''),
            genre: genres[randomInt(0, genres.length - 1)],
            location: locations[randomInt(0, locations.length - 1)],
            status: Math.random() > 0.2 ? 'STATUS_PUBLISHED' : 'STATUS_CREATED',
            type: Math.random() > 0.8 ? 'VIRTUAL_ACT_PROFILE' : 'ACT_PROFILE',
            createdAt: new Date(Date.now() - randomInt(1, 365) * 86400000).toISOString(),
            description: 'Sample act description'
        });
    }
    return acts;
}

function generateMockVenues(count) {
    const venueNames = ['The Blue Note', 'Concert Hall', 'Jazz Corner', 'Rock Arena', 'Classical Theatre', 
                       'Electronic Club', 'Folk House', 'Music Palace', 'Sound Stage', 'Live Venue'];
    const locations = ['Berlin', 'New York', 'Denver', 'Los Angeles', 'London', 'Nashville', 'Tokyo', 'Vienna', 'Chicago', 'Amsterdam'];
    
    const venues = [];
    for (let i = 0; i < count; i++) {
        venues.push({
            name: venueNames[i % venueNames.length] + (i >= venueNames.length ? ` ${Math.floor(i / venueNames.length) + 1}` : ''),
            location: locations[randomInt(0, locations.length - 1)],
            status: Math.random() > 0.2 ? 'STATUS_PUBLISHED' : 'STATUS_CREATED',
            type: Math.random() > 0.8 ? 'VIRTUAL_VENUE_PROFILE' : 'VENUE_PROFILE',
            createdAt: new Date(Date.now() - randomInt(1, 365) * 86400000).toISOString(),
            description: 'Sample venue description'
        });
    }
    return venues;
}