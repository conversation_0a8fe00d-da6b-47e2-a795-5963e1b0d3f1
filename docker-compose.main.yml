# StageMinder Complete System Docker Compose
# This file manages the entire StageMinder ecosystem with shared Neo4j
# 
# Usage:
#   Start everything: docker-compose -f docker-compose.main.yml up -d
#   Start only Neo4j: docker-compose -f docker-compose.main.yml up -d neo4j
#   Start StageMinder: docker-compose -f docker-compose.main.yml up -d stageminder-backend stageminder-frontend stageminder-nginx
#   Start AdminConsole: docker-compose -f docker-compose.main.yml up -d admin-console admin-frontend

services:
  # =================================================================
  # SHARED NEO4J DATABASE SERVICE
  # =================================================================
  neo4j:
    image: neo4j:5.15-community
    container_name: stageminder-neo4j-shared
    environment:
      - NEO4J_AUTH=neo4j/stageminder2024
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_memory_pagecache_size=512m
      - NEO4J_dbms_connector_bolt_listen__address=0.0.0.0:7687
      - NEO4J_dbms_connector_http_listen__address=0.0.0.0:7474
      - NEO4J_dbms_default__database=neo4j
    ports:
      - "7474:7474"
      - "7687:7687"
    volumes:
      - stageminder_neo4j_data:/data
      - stageminder_neo4j_logs:/logs
      - stageminder_neo4j_conf:/conf
    networks:
      - stageminder-shared-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "cypher-shell -u neo4j -p stageminder2024 'RETURN 1'"]
      interval: 30s
      timeout: 10s
      retries: 5

  # =================================================================
  # STAGEMINDER MAIN APPLICATION SERVICES
  # =================================================================
  stageminder-backend:
    build:
      context: ./StageMinder/Build/backend
      dockerfile: Dockerfile
    container_name: stageminder-backend
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=stageminder2024
      - AWS_ACCESS_KEY_ID=your_aws_access_key_here
      - AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
      - AWS_REGION=us-east-1
      - S3_BUCKET_NAME=your_s3_bucket_name_here
      - SERVER_ADDRESS=0.0.0.0
    ports:
      - "8080:8080"
    depends_on:
      neo4j:
        condition: service_healthy
    networks:
      - stageminder-shared-network
    restart: unless-stopped

  stageminder-frontend:
    build:
      context: ./StageMinder/Build/frontend
      dockerfile: Dockerfile
    container_name: stageminder-frontend
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://localhost/api
      - HOSTNAME=0.0.0.0
    ports:
      - "3000:3000"
    depends_on:
      - stageminder-backend
    networks:
      - stageminder-shared-network
    restart: unless-stopped

  stageminder-nginx:
    image: nginx:alpine
    container_name: stageminder-nginx
    ports:
      - "80:80"
    depends_on:
      - stageminder-frontend
      - stageminder-backend
    networks:
      - stageminder-shared-network
    restart: unless-stopped
    volumes:
      - ./StageMinder/Scripts/Configuration/nginx/nginx.conf:/etc/nginx/nginx.conf:ro

  # =================================================================
  # ADMIN CONSOLE SERVICES
  # =================================================================
  admin-console:
    build:
      context: ./adminconsole
      dockerfile: Dockerfile
    container_name: stageminder-admin-console
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_NEO4J_URI=bolt://neo4j:7687
      - SPRING_NEO4J_AUTHENTICATION_USERNAME=neo4j
      - SPRING_NEO4J_AUTHENTICATION_PASSWORD=stageminder2024
      - STAGEMINDER_BACKEND_URL=http://stageminder-backend:8080
      - MANAGEMENT_METRICS_BINDERS_SYSTEM_ENABLED=false
    ports:
      - "8081:8081"
    depends_on:
      neo4j:
        condition: service_healthy
    networks:
      - stageminder-shared-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Admin Frontend (Nginx)
  admin-frontend:
    image: nginx:alpine
    container_name: admin-frontend
    ports:
      - "3001:80"
    volumes:
      - ./adminconsole/index.html:/usr/share/nginx/html/index.html:ro
      - ./adminconsole/style.css:/usr/share/nginx/html/style.css:ro
      - ./adminconsole/script.js:/usr/share/nginx/html/script.js:ro
      - ./adminconsole/logo.webp:/usr/share/nginx/html/logo.webp:ro
    networks:
      - stageminder-shared-network
    restart: unless-stopped
    depends_on:
      - admin-console



volumes:
  stageminder_neo4j_data:
    driver: local
    name: stageminder_neo4j_data
  stageminder_neo4j_logs:
    driver: local  
    name: stageminder_neo4j_logs
  stageminder_neo4j_conf:
    driver: local
    name: stageminder_neo4j_conf

networks:
  stageminder-shared-network:
    driver: bridge
    name: stageminder-shared-network
