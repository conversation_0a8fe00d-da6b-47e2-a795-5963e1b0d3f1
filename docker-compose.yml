services:
  # Admin Console Service (连接到现有的stageminder neo4j)
  admin-console:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: stageminder-admin-console
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_NEO4J_URI=bolt://stageminder-neo4j:7687
      - SPRING_NEO4J_AUTHENTICATION_USERNAME=neo4j
      - SPRING_NEO4J_AUTHENTICATION_PASSWORD=stageminder2024
      - STAGEMINDER_BACKEND_URL=http://stageserver:8080
      - MANAGEMENT_METRICS_BINDERS_SYSTEM_ENABLED=false   # 关键：禁用 ProcessorMetrics
    ports:
      - "8081:8081"
    networks:
      - stageminder_stageminder-network
    restart: unless-stopped

  # Admin Frontend (Nginx)
  admin-frontend:
    image: nginx:alpine
    container_name: admin-frontend
    ports:
      - "3001:80"
    volumes:
      - ./index.html:/usr/share/nginx/html/index.html:ro
      - ./style.css:/usr/share/nginx/html/style.css:ro
      - ./script.js:/usr/share/nginx/html/script.js:ro
      - ./logo.webp:/usr/share/nginx/html/logo.webp:ro
    networks:
      - stageminder_stageminder-network
    restart: unless-stopped

networks:
  stageminder_stageminder-network:
    external: true