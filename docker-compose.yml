version: '3.8'

services:
  # Neo4j Database
  neo4j:
    image: neo4j:5.15-community
    container_name: stageminder-neo4j
    environment:
      - NEO4J_AUTH=neo4j/stageminder2024
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_connector_bolt_listen__address=0.0.0.0:7687
      - NEO4J_dbms_connector_http_listen__address=0.0.0.0:7474
    ports:
      - "7474:7474"
      - "7687:7687"
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_conf:/conf
    networks:
      - stageminder-network
    restart: unless-stopped

  # Backend Service
  backend:
    build:
      context: ./Build/backend
      dockerfile: Dockerfile
    container_name: stageminder-backend
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=stageminder2024
      # 替换为您的 AWS 配置
      - AWS_ACCESS_KEY_ID=your_aws_access_key_here
      - AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
      - AWS_REGION=us-east-1
      - S3_BUCKET_NAME=your_s3_bucket_name_here
      - SERVER_ADDRESS=0.0.0.0
    ports:
      - "8080:8080"
    depends_on:
      - neo4j
    networks:
      - stageminder-network
    restart: unless-stopped

  # Frontend Service
  frontend:
    build:
      context: ./Build/frontend
      dockerfile: Dockerfile
    container_name: stageminder-frontend
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://localhost:8080/api/v1
      - HOSTNAME=0.0.0.0
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - stageminder-network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: stageminder-nginx
    ports:
      - "80:80"
    depends_on:
      - frontend
      - backend
    networks:
      - stageminder-network
    restart: unless-stopped
    volumes:
      - ./Scripts/Configuration/nginx/nginx.conf:/etc/nginx/nginx.conf:ro

volumes:
  neo4j_data:
  neo4j_logs:
  neo4j_conf:

networks:
  stageminder-network:
    driver: bridge 