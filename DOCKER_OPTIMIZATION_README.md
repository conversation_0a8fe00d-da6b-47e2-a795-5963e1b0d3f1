# StageMinder Docker 优化配置指南

should be in english


## 概览

这个优化配置将 Neo4j 数据库提取为独立服务，让 `StageMinder` 和 `AdminConsole` 两个项目可以共享同一个 Neo4j 实例。这种架构具有以下优势：

✅ **资源效率**: 只需要一个 Neo4j 实例
✅ **数据一致性**: 两个项目共享同一个数据库
✅ **独立部署**: 各个项目可以独立启动/停止
✅ **简化维护**: 统一的数据库管理
✅ **开发友好**: 支持开发和生产环境
✅ **跨平台支持**: Windows 和 Linux 系统兼容

## 架构说明

```
┌─────────────────────────────────────────────────────────────┐
│                    StageMinder System                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                 │
│  │  StageMinder    │    │  AdminConsole   │                 │
│  │  ┌─────────────┐│    │  ┌─────────────┐│                 │
│  │  │  Frontend   ││    │  │  Frontend   ││                 │
│  │  │  :3000      ││    │  │  :3001      ││                 │
│  │  └─────────────┘│    │  └─────────────┘│                 │
│  │  ┌─────────────┐│    │  ┌─────────────┐│                 │
│  │  │  Backend    ││    │  │  Backend    ││                 │
│  │  │  :8080      ││    │  │  :8081      ││                 │
│  │  └─────────────┘│    │  └─────────────┘│                 │
│  │  ┌─────────────┐│    │                 │                 │
│  │  │   Nginx     ││    │                 │                 │
│  │  │   :80       ││    │                 │                 │
│  │  └─────────────┘│    │                 │                 │
│  └─────────────────┘    └─────────────────┘                 │
│           │                       │                         │
│           └───────────┬───────────┘                         │
│                       │                                     │
│              ┌─────────────────┐                            │
│              │     Neo4j       │                            │
│              │  :7474, :7687   │                            │
│              │   (Shared DB)   │                            │
│              └─────────────────┘                            │
└─────────────────────────────────────────────────────────────┘
```

## 文件结构

```
StageMinderRepo-main/
├── docker-compose.yml                # 原始完整配置（兼容性保留）
├── docker-compose.neo4j.yml          # 独立的 Neo4j 服务
├── docker-compose.main.yml           # 完整系统管理
├── docker-control.sh                 # Linux/Mac 控制脚本
├── docker-control.bat                # Windows 控制脚本
├── DOCKER_OPTIMIZATION_README.md     # 本文档
│
├── StageMinder/
│   ├── Build/
│   │   ├── backend/Dockerfile         # 后端生产环境镜像
│   │   └── frontend/
│   │       ├── Dockerfile             # 前端生产环境镜像
│   │       └── Dockerfile.dev         # 前端开发环境镜像
│   ├── docker-compose.yml            # 原始配置（兼容性保留）
│   ├── docker-compose-optimized.yml       # 生产环境配置
│   └── docker-compose-local-optimized.yml # 开发环境配置
│
└── adminconsole/
    ├── Dockerfile                     # Admin Console 后端镜像
    ├── Dockerfile.frontend            # Admin Console 前端镜像
    ├── docker-compose.yml            # 原始配置（兼容性保留）
    └── docker-compose-optimized.yml       # Admin Console 优化配置
```

## 快速开始

### 方法一：使用控制脚本（推荐）

#### Windows 本地测试部署
```cmd
# 启动完整系统（生产模式）
.\docker-control.bat start

# 启动开发环境（热重载）
.\docker-control.bat start dev

# 启动单个服务
.\docker-control.bat start neo4j
.\docker-control.bat start stageminder
.\docker-control.bat start admin

# 查看状态
.\docker-control.bat status

# 查看日志
.\docker-control.bat logs
.\docker-control.bat logs neo4j

# 停止所有服务
.\docker-control.bat stop

# 重启服务
.\docker-control.bat restart

# 清理数据（危险操作！）
.\docker-control.bat cleanup
```

#### Linux 服务器部署
```bash
# 添加执行权限（首次运行）
chmod +x docker-control.sh

# 启动完整系统（生产模式）
./docker-control.sh start

# 启动开发环境（热重载）
./docker-control.sh start dev

# 启动单个服务
./docker-control.sh start neo4j
./docker-control.sh start stageminder
./docker-control.sh start admin

# 查看状态
./docker-control.sh status

# 查看日志
./docker-control.sh logs
./docker-control.sh logs backend

# 停止所有服务
./docker-control.sh stop

# 重启服务
./docker-control.sh restart

# 清理数据（危险操作！）
./docker-control.sh cleanup
```

### 方法二：手动启动

#### Windows 系统
```cmd
# 1. 启动共享 Neo4j 数据库
docker-compose -f docker-compose.neo4j.yml up -d

# 2. 启动 StageMinder 应用
cd StageMinder
docker-compose -f docker-compose-optimized.yml up -d
cd ..

# 3. 启动 AdminConsole
cd adminconsole
docker-compose -f docker-compose-optimized.yml up -d
cd ..
```

#### Linux 系统
```bash
# 1. 启动共享 Neo4j 数据库
docker-compose -f docker-compose.neo4j.yml up -d

# 2. 启动 StageMinder 应用
cd StageMinder
docker-compose -f docker-compose-optimized.yml up -d
cd ..

# 3. 启动 AdminConsole
cd adminconsole
docker-compose -f docker-compose-optimized.yml up -d
cd ..
```

## 详细使用说明

### 控制脚本命令

| 命令 | Windows | Linux | 说明 |
|------|---------|-------|------|
| 启动完整系统 | `.\docker-control.bat start` | `./docker-control.sh start` | 启动完整系统（生产模式） |
| 启动开发环境 | `.\docker-control.bat start dev` | `./docker-control.sh start dev` | 启动开发环境（热重载） |
| 启动 Neo4j | `.\docker-control.bat start neo4j` | `./docker-control.sh start neo4j` | 仅启动 Neo4j 数据库 |
| 启动 StageMinder | `.\docker-control.bat start stageminder` | `./docker-control.sh start stageminder` | 仅启动 StageMinder 应用 |
| 启动 Admin Console | `.\docker-control.bat start admin` | `./docker-control.sh start admin` | 仅启动 Admin Console |
| 停止所有服务 | `.\docker-control.bat stop` | `./docker-control.sh stop` | 停止所有服务 |
| 重启服务 | `.\docker-control.bat restart` | `./docker-control.sh restart` | 重启所有服务 |
| 查看状态 | `.\docker-control.bat status` | `./docker-control.sh status` | 显示服务状态 |
| 查看日志 | `.\docker-control.bat logs [service]` | `./docker-control.sh logs [service]` | 显示日志 |
| 清理数据 | `.\docker-control.bat cleanup` | `./docker-control.sh cleanup` | 清理所有数据（危险操作！） |

### 服务访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| Neo4j Browser | http://localhost:7474 | 数据库管理界面 |
| StageMinder 应用 | http://localhost | 主应用（通过 Nginx 代理） |
| StageMinder Frontend | http://localhost:3000 | 前端直接访问 |
| StageMinder API | http://localhost:8080 | 后端 API |
| Admin Console UI | http://localhost:3001 | 管理界面 |
| Admin Console API | http://localhost:8081 | 管理 API |

**Neo4j 登录信息：**
- 用户名: `neo4j`
- 密码: `stageminder2024`

### 系统差异说明

#### Windows 本地测试部署特点
- 使用 `.bat` 批处理脚本
- 支持 PowerShell 和 CMD
- 路径使用反斜杠 `\`
- 容器内存限制相对宽松
- 适合开发和测试环境

#### Linux 服务器部署特点
- 使用 `.sh` Shell 脚本
- 需要执行权限：`chmod +x docker-control.sh`
- 路径使用正斜杠 `/`
- 更严格的资源管理
- 适合生产环境部署

## 环境配置

### 生产环境配置

#### Windows 生产环境
```cmd
# 使用主配置文件启动
.\docker-control.bat start

# 或手动启动
docker-compose -f docker-compose.main.yml up -d
```

#### Linux 生产环境
```bash
# 使用主配置文件启动
./docker-control.sh start

# 或手动启动
docker-compose -f docker-compose.main.yml up -d
```

**生产环境特点：**
- 完整的服务栈
- 优化的资源配置
- 自动重启策略
- 健康检查
- 多阶段构建优化
- 安全用户权限

### 开发环境配置

#### Windows 开发环境
```cmd
# 启动开发环境
.\docker-control.bat start dev

# 或手动启动
docker-compose -f docker-compose.neo4j.yml up -d
cd StageMinder
docker-compose -f docker-compose-local-optimized.yml up -d
cd ..\adminconsole
docker-compose -f docker-compose-optimized.yml up -d
cd ..
```

#### Linux 开发环境
```bash
# 启动开发环境
./docker-control.sh start dev

# 或手动启动
docker-compose -f docker-compose.neo4j.yml up -d
cd StageMinder
docker-compose -f docker-compose-local-optimized.yml up -d
cd ../adminconsole
docker-compose -f docker-compose-optimized.yml up -d
cd ..
```

**开发环境特点：**
- 代码热重载
- 开发工具支持
- 源码挂载
- LiveReload 功能
- 快速构建
- 调试端口开放

## 数据持久化

所有数据存储在命名卷中：
- `stageminder_neo4j_data`: 数据库文件
- `stageminder_neo4j_logs`: 日志文件
- `stageminder_neo4j_conf`: 配置文件
- `stageminder_neo4j_import`: 数据导入目录
- `stageminder_neo4j_plugins`: 插件目录
- `stageminder_maven_cache`: Maven 缓存（开发环境）

## 网络配置

使用统一的 Docker 网络：
- 网络名: `stageminder-shared-network`
- 类型: bridge
- 所有服务都连接到此网络
- 支持服务间通信
- 外部网络访问控制

## 常见问题

### 1. Neo4j 启动失败

**问题**: Neo4j 容器启动后立即退出

**Windows 解决方案**:
```cmd
# 查看日志
docker logs stageminder-neo4j-shared

# 检查内存配置
docker stats

# 检查 Docker Desktop 内存设置
# 如果内存不足，调整 docker-compose.neo4j.yml 中的内存设置
```

**Linux 解决方案**:
```bash
# 查看日志
docker logs stageminder-neo4j-shared

# 检查系统内存
free -h

# 检查内存配置
docker stats

# 如果内存不足，调整 docker-compose.neo4j.yml 中的内存设置
```

### 2. 服务无法连接到 Neo4j

**问题**: 应用报告无法连接到数据库

**Windows 解决方案**:
```cmd
# 1. 确保 Neo4j 完全启动
.\docker-control.bat status

# 2. 检查网络连接
docker network ls | findstr stageminder

# 3. 测试连接
docker exec stageminder-neo4j-shared cypher-shell -u neo4j -p stageminder2024 "RETURN 1"
```

**Linux 解决方案**:
```bash
# 1. 确保 Neo4j 完全启动
./docker-control.sh status

# 2. 检查网络连接
docker network ls | grep stageminder

# 3. 测试连接
docker exec stageminder-neo4j-shared cypher-shell -u neo4j -p stageminder2024 "RETURN 1"
```

### 3. 端口冲突

**问题**: 端口已被占用

**Windows 解决方案**:
```cmd
# 查看端口占用
netstat -an | findstr :7474
netstat -an | findstr :8080

# 或使用 PowerShell
Get-NetTCPConnection -LocalPort 7474
Get-NetTCPConnection -LocalPort 8080

# 修改 docker-compose 文件中的端口映射
```

**Linux 解决方案**:
```bash
# 查看端口占用
netstat -tulpn | grep :7474
netstat -tulpn | grep :8080

# 或使用 ss 命令
ss -tulpn | grep :7474

# 修改 docker-compose 文件中的端口映射
```

### 4. 权限问题

**Windows 权限问题**:
```cmd
# 确保以管理员身份运行 PowerShell 或 CMD
# 检查 Docker Desktop 是否正常运行
# 确保用户在 docker-users 组中
```

**Linux 权限问题**:
```bash
# 给脚本添加执行权限
chmod +x docker-control.sh

# 将用户添加到 docker 组
sudo usermod -aG docker $USER

# 如果是数据卷权限问题
sudo chown -R $(id -u):$(id -g) /var/lib/docker/volumes/stageminder_neo4j_data

# 重新登录以应用组权限
```

## 迁移指南

### 从旧配置迁移

如果你之前使用的是包含 Neo4j 的配置，请按以下步骤迁移：

#### Windows 系统迁移
1. **备份数据（重要！）**
```cmd
# 导出现有数据
docker exec <old-neo4j-container> neo4j-admin dump --database=neo4j --to=/tmp/backup.dump
docker cp <old-neo4j-container>:/tmp/backup.dump .\neo4j-backup.dump
```

2. **停止旧服务**
```cmd
docker-compose down
```

3. **启动新的 Neo4j 服务**
```cmd
docker-compose -f docker-compose.neo4j.yml up -d
```

4. **恢复数据**
```cmd
# 复制备份文件到新容器
docker cp .\neo4j-backup.dump stageminder-neo4j-shared:/tmp/backup.dump

# 停止 Neo4j 进行恢复
docker-compose -f docker-compose.neo4j.yml stop
docker exec stageminder-neo4j-shared neo4j-admin load --from=/tmp/backup.dump --database=neo4j --force
docker-compose -f docker-compose.neo4j.yml start
```

5. **启动应用服务**
```cmd
.\docker-control.bat start
```

#### Linux 系统迁移
1. **备份数据（重要！）**
```bash
# 导出现有数据
docker exec <old-neo4j-container> neo4j-admin dump --database=neo4j --to=/tmp/backup.dump
docker cp <old-neo4j-container>:/tmp/backup.dump ./neo4j-backup.dump
```

2. **停止旧服务**
```bash
docker-compose down
```

3. **启动新的 Neo4j 服务**
```bash
docker-compose -f docker-compose.neo4j.yml up -d
```

4. **恢复数据**
```bash
# 复制备份文件到新容器
docker cp ./neo4j-backup.dump stageminder-neo4j-shared:/tmp/backup.dump

# 停止 Neo4j 进行恢复
docker-compose -f docker-compose.neo4j.yml stop
docker exec stageminder-neo4j-shared neo4j-admin load --from=/tmp/backup.dump --database=neo4j --force
docker-compose -f docker-compose.neo4j.yml start
```

5. **启动应用服务**
```bash
./docker-control.sh start
```

### 回滚到旧配置

#### Windows 回滚
```cmd
# 停止新服务
.\docker-control.bat stop

# 使用旧的 docker-compose 文件
docker-compose -f docker-compose.yml up -d
```

#### Linux 回滚
```bash
# 停止新服务
./docker-control.sh stop

# 使用旧的 docker-compose 文件
docker-compose -f docker-compose.yml up -d
```

## 监控和维护

### 查看资源使用情况

#### Windows 系统
```cmd
# 查看容器资源使用
docker stats

# 查看磁盘使用
docker system df

# 查看卷使用情况
docker volume ls

# 查看网络使用
docker network ls
```

#### Linux 系统
```bash
# 查看容器资源使用
docker stats

# 查看磁盘使用
docker system df

# 查看卷使用情况
docker volume ls

# 查看系统资源
htop
df -h
```

### 日志管理

#### Windows 日志管理
```cmd
# 查看特定服务日志
.\docker-control.bat logs neo4j
.\docker-control.bat logs backend

# 查看实时日志
docker logs -f stageminder-neo4j-shared

# 清理日志（如果需要）
docker system prune -f
```

#### Linux 日志管理
```bash
# 查看特定服务日志
./docker-control.sh logs neo4j
./docker-control.sh logs backend

# 查看实时日志
docker logs -f stageminder-neo4j-shared

# 清理日志（如果需要）
docker system prune -f

# 查看系统日志
journalctl -u docker
```

### 定期维护

#### Windows 维护
```cmd
# 清理未使用的镜像和容器
docker system prune -f

# 更新镜像
docker-compose -f docker-compose.neo4j.yml pull
docker-compose -f docker-compose.main.yml pull

# 重建镜像
docker-compose -f docker-compose.main.yml build --no-cache
```

#### Linux 维护
```bash
# 清理未使用的镜像和容器
docker system prune -f

# 更新镜像
docker-compose -f docker-compose.neo4j.yml pull
docker-compose -f docker-compose.main.yml pull

# 重建镜像
docker-compose -f docker-compose.main.yml build --no-cache

# 清理系统缓存
sync && echo 3 > /proc/sys/vm/drop_caches
```

## 性能优化

### Neo4j 性能调优

#### Windows 系统优化
在 `docker-compose.neo4j.yml` 中调整以下参数：

```yaml
environment:
  # 根据 Windows 系统内存调整（推荐 8GB+ 系统）
  - NEO4J_dbms_memory_heap_initial__size=512m
  - NEO4J_dbms_memory_heap_max__size=2G
  - NEO4J_dbms_memory_pagecache_size=512m

  # 查询性能
  - NEO4J_dbms_logs_query_enabled=INFO
  - NEO4J_dbms_logs_query_threshold=1s
```

#### Linux 服务器优化
在 `docker-compose.neo4j.yml` 中调整以下参数：

```yaml
environment:
  # 根据服务器内存调整（推荐 16GB+ 服务器）
  - NEO4J_dbms_memory_heap_initial__size=1G
  - NEO4J_dbms_memory_heap_max__size=4G
  - NEO4J_dbms_memory_pagecache_size=2G

  # 查询性能
  - NEO4J_dbms_logs_query_enabled=INFO
  - NEO4J_dbms_logs_query_threshold=1s

  # 生产环境优化
  - NEO4J_dbms_tx_log_rotation_retention_policy=1G size
  - NEO4J_dbms_checkpoint_interval_time=15m
```

### 容器资源限制

#### Windows 开发环境
```yaml
deploy:
  resources:
    limits:
      memory: 2G
      cpus: '1'
    reservations:
      memory: 1G
      cpus: '0.5'
```

#### Linux 生产环境
```yaml
deploy:
  resources:
    limits:
      memory: 8G
      cpus: '4'
    reservations:
      memory: 4G
      cpus: '2'
```

## 安全考虑

### Windows 本地测试安全
1. **防火墙配置**: 确保 Windows 防火墙允许 Docker 端口
2. **用户权限**: 确保用户在 docker-users 组中
3. **网络隔离**: 使用 Docker Desktop 的网络隔离功能

### Linux 服务器安全
1. **更改默认密码**: 在生产环境中修改 Neo4j 密码
2. **网络隔离**: 使用自定义网络隔离服务
3. **访问控制**: 配置 iptables 或 ufw 限制端口访问
4. **TLS 加密**: 在生产环境中启用 HTTPS
5. **用户权限**: 使用非 root 用户运行容器
6. **定期更新**: 保持 Docker 和镜像版本最新

## 部署命令总结

### Windows 本地测试部署
```cmd
# 完整部署
.\docker-control.bat start

# 开发环境
.\docker-control.bat start dev

# 查看状态
.\docker-control.bat status

# 停止服务
.\docker-control.bat stop
```

### Linux 服务器部署
```bash
# 添加权限（首次）
chmod +x docker-control.sh

# 完整部署
./docker-control.sh start

# 开发环境
./docker-control.sh start dev

# 查看状态
./docker-control.sh status

# 停止服务
./docker-control.sh stop
```

## 支持

### Windows 系统问题排查
1. 查看日志：`.\docker-control.bat logs`
2. 检查状态：`.\docker-control.bat status`
3. 检查 Docker Desktop 状态
4. 参考本文档的常见问题部分

### Linux 系统问题排查
1. 查看日志：`./docker-control.sh logs`
2. 检查状态：`./docker-control.sh status`
3. 检查 Docker 服务：`systemctl status docker`
4. 参考本文档的常见问题部分
5. 查看 Docker 官方文档

---

**注意**:
- **Windows 用户**: 这个优化配置适合本地开发和测试，建议使用 Docker Desktop
- **Linux 用户**: 这个优化配置已经过生产环境测试，但请在部署前在测试环境验证
- **跨平台**: 所有配置文件都支持 Windows 和 Linux 系统
