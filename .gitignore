# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Production builds
.next/
out/
dist/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Logs
logs/
*.log

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Java/Maven
target/
*.jar
*.war
*.ear
*.class
.mvn/wrapper/maven-wrapper.jar
!.mvn/wrapper/maven-wrapper.properties

# Spring Boot
*.orig
spring.log

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
*.tmp
*.temp
*.cache

# Package files
*.tgz
*.tar.gz

# Next.js
.next/
.vercel/

# Misc
*.tsbuildinfo
.turbo/