@echo off
REM StageMinder Docker Control Script for Windows
REM This script provides convenient commands to manage the entire StageMinder ecosystem
REM with shared Neo4j database

setlocal enabledelayedexpansion

REM Function to check if Dock<PERSON> is running
:check_docker
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not running. Please start Docker Desktop first.
    exit /b 1
)
goto :eof

REM Function to start Neo4j only
:start_neo4j
echo [INFO] Starting shared Neo4j database...
docker-compose -f docker-compose.neo4j.yml up -d

echo [INFO] Waiting for Neo4j to be ready...
timeout /t 10 /nobreak >nul

REM Wait for Neo4j to be healthy
for /l %%i in (1,1,30) do (
    docker exec stageminder-neo4j-shared cypher-shell -u neo4j -p stageminder2024 "RETURN 1" >nul 2>&1
    if not errorlevel 1 (
        echo [SUCCESS] Neo4j is ready!
        goto :eof
    )
    echo [INFO] Waiting for Neo4j... (%%i/30)
    timeout /t 2 /nobreak >nul
)

echo [ERROR] Neo4j failed to start properly
exit /b 1

REM Function to start everything
:start_all
call :check_docker
echo [INFO] Starting complete StageMinder system...
docker-compose -f docker-compose.main.yml up -d
echo [SUCCESS] All services started!
echo [INFO] Services will be available at:
echo   • Neo4j Browser: http://localhost:7474
echo   • StageMinder App: http://localhost
echo   • StageMinder API: http://localhost:8080
echo   • Admin Console API: http://localhost:8081
echo   • Admin Console UI: http://localhost:3001
goto :eof

REM Function to start only StageMinder
:start_stageminder
call :check_docker
echo [INFO] Starting StageMinder application...

REM Check if Neo4j is running
docker ps | findstr "stageminder-neo4j-shared" >nul
if errorlevel 1 (
    echo [WARNING] Neo4j is not running. Starting Neo4j first...
    call :start_neo4j
)

cd StageMinder
docker-compose -f docker-compose-optimized.yml up -d
cd ..
echo [SUCCESS] StageMinder started!
echo [INFO] Available at: http://localhost
goto :eof

REM Function to start only Admin Console
:start_admin
call :check_docker
echo [INFO] Starting Admin Console...

REM Check if Neo4j is running
docker ps | findstr "stageminder-neo4j-shared" >nul
if errorlevel 1 (
    echo [WARNING] Neo4j is not running. Starting Neo4j first...
    call :start_neo4j
)

cd adminconsole
docker-compose -f docker-compose-optimized.yml up -d
cd ..
echo [SUCCESS] Admin Console started!
echo [INFO] Available at:
echo   • Admin Console API: http://localhost:8081
echo   • Admin Console UI: http://localhost:3001
goto :eof

REM Function to start development environment
:start_dev
call :check_docker
echo [INFO] Starting development environment...

REM Start Neo4j first
docker ps | findstr "stageminder-neo4j-shared" >nul
if errorlevel 1 (
    call :start_neo4j
)

REM Start StageMinder in development mode
cd StageMinder
docker-compose -f docker-compose-local-optimized.yml up -d
cd ..

REM Start Admin Console
cd adminconsole
docker-compose -f docker-compose-optimized.yml up -d
cd ..

echo [SUCCESS] Development environment started!
echo [INFO] Services available at:
echo   • Neo4j Browser: http://localhost:7474
echo   • StageMinder (dev): http://localhost
echo   • Admin Console: http://localhost:3001
goto :eof

REM Function to stop all services
:stop_all
echo [INFO] Stopping all StageMinder services...

REM Stop main compose services
docker-compose -f docker-compose.main.yml down 2>nul

REM Stop individual services
if exist StageMinder (
    cd StageMinder
    docker-compose -f docker-compose-optimized.yml down 2>nul
    docker-compose -f docker-compose-local-optimized.yml down 2>nul
    cd ..
)

if exist adminconsole (
    cd adminconsole
    docker-compose -f docker-compose-optimized.yml down 2>nul
    cd ..
)

REM Stop Neo4j
docker-compose -f docker-compose.neo4j.yml down 2>nul

echo [SUCCESS] All services stopped!
goto :eof

REM Function to show status
:show_status
echo [INFO] StageMinder Services Status:
echo.

set containers=stageminder-neo4j-shared stageminder-backend stageminder-frontend stageminder-nginx stageminder-admin-console admin-frontend

for %%c in (%containers%) do (
    docker ps --format "table {{.Names}}" | findstr "%%c" >nul 2>&1
    if not errorlevel 1 (
        echo   ✅ %%c: Running
    ) else (
        echo   ❌ %%c: Not running
    )
)

echo.
echo [INFO] Access URLs:
echo   • Neo4j Browser: http://localhost:7474 (neo4j/stageminder2024)
echo   • StageMinder: http://localhost
echo   • Admin Console: http://localhost:3001
goto :eof

REM Function to show logs
:show_logs
if "%~2"=="" (
    echo [INFO] Showing logs for all services...
    docker-compose -f docker-compose.main.yml logs -f
) else (
    echo [INFO] Showing logs for %~2...
    docker logs -f %~2 2>nul || docker-compose -f docker-compose.main.yml logs -f %~2
)
goto :eof

REM Function to cleanup volumes
:cleanup
echo [WARNING] This will remove all Neo4j data! Are you sure? (y/N)
set /p response=
if /i "!response!"=="y" (
    call :stop_all
    echo [INFO] Removing Docker volumes...
    docker volume rm stageminder_neo4j_data stageminder_neo4j_logs stageminder_neo4j_conf 2>nul
    docker volume rm stageminder_maven_cache 2>nul
    echo [SUCCESS] Cleanup completed!
) else (
    echo [INFO] Cleanup cancelled.
)
goto :eof

REM Main script logic
if "%1"=="start" (
    if "%2"=="neo4j" (
        call :start_neo4j
    ) else if "%2"=="stageminder" (
        call :start_stageminder
    ) else if "%2"=="admin" (
        call :start_admin
    ) else if "%2"=="dev" (
        call :start_dev
    ) else (
        call :start_all
    )
) else if "%1"=="stop" (
    call :stop_all
) else if "%1"=="restart" (
    call :stop_all
    timeout /t 2 /nobreak >nul
    call :start_all
) else if "%1"=="status" (
    call :show_status
) else if "%1"=="logs" (
    call :show_logs %*
) else if "%1"=="cleanup" (
    call :cleanup
) else (
    echo StageMinder Docker Control Script for Windows
    echo.
    echo Usage: %0 {command} [options]
    echo.
    echo Commands:
    echo   start [service]     Start services
    echo     start             Start all services (production)
    echo     start neo4j       Start only Neo4j database
    echo     start stageminder Start only StageMinder app
    echo     start admin       Start only Admin Console
    echo     start dev         Start development environment
    echo.
    echo   stop                Stop all services
    echo   restart             Restart all services
    echo   status              Show service status
    echo   logs [service]      Show logs (all or specific service)
    echo   cleanup             Remove all data (destructive!)
    echo.
    echo Examples:
    echo   %0 start            # Start everything
    echo   %0 start dev        # Start development environment
    echo   %0 stop             # Stop everything
    echo   %0 logs backend     # Show backend logs
    echo   %0 status           # Show status of all services
)
