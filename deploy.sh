#!/bin/bash

# StageMinder Deployment Script for IP-based setup

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        echo "Error: Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Function to generate SSL certificates
generate_ssl() {
    if [ ! -f "Scripts/Configuration/nginx/SSL/nginx.crt" ]; then
        echo "Generating SSL certificates..."
        mkdir -p Scripts/Configuration/nginx/SSL
        
        openssl genrsa -out Scripts/Configuration/nginx/SSL/nginx.key 2048
        openssl req -new -key Scripts/Configuration/nginx/SSL/nginx.key -out Scripts/Configuration/nginx/SSL/nginx.csr -subj "/C=US/ST=State/L=City/O=StageMinder/CN=***********"
        openssl x509 -req -days 365 -in Scripts/Configuration/nginx/SSL/nginx.csr -signkey Scripts/Configuration/nginx/SSL/nginx.key -out Scripts/Configuration/nginx/SSL/nginx.crt
        rm Scripts/Configuration/nginx/SSL/nginx.csr
        
        echo "SSL certificates generated!"
    fi
}

case "$1" in
    start)
        echo "Starting StageMinder services..."
        check_docker
        docker-compose up -d
        echo "Services started! Access at http://***********"
        ;;
    start-https)
        echo "Starting StageMinder services with HTTPS..."
        check_docker
        generate_ssl
        docker-compose -f docker-compose-https.yml up -d
        echo "Services started! Access at https://***********"
        echo "Note: You'll need to accept the self-signed certificate warning in your browser"
        ;;
    stop)
        echo "Stopping StageMinder services..."
        docker-compose down
        docker-compose -f docker-compose-https.yml down 2>/dev/null || true
        ;;
    restart)
        echo "Restarting StageMinder services..."
        $0 stop
        sleep 5
        $0 start
        ;;
    update)
        echo "Updating StageMinder..."
        docker-compose down
        docker-compose build --no-cache
        docker-compose up -d
        ;;
    logs)
        docker-compose logs -f
        ;;
    status)
        docker-compose ps
        ;;
    *)
        echo "Usage: $0 {start|start-https|stop|restart|update|logs|status}"
        echo ""
        echo "Commands:"
        echo "  start       - Start with HTTP only"
        echo "  start-https - Start with HTTPS (self-signed certificate)"
        echo "  stop        - Stop all services"
        echo "  restart     - Restart all services"
        echo "  update      - Rebuild and restart services"
        echo "  logs        - Show service logs"
        echo "  status      - Show service status"
        exit 1
        ;;
esac 