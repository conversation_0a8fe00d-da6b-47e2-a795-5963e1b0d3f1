# StageMinder Local Development (without Neo4j)
# This configuration connects to shared Neo4j service for local development
# 
# Prerequisites: 
#   1. Start shared Neo4j first: docker-compose -f docker-compose.neo4j.yml up -d
#
# Usage:
#   docker-compose -f docker-compose-local-optimized.yml up -d

services:
  # Backend Service - Development Mode with Hot Reload
  backend:
    image: maven:3.9-eclipse-temurin-21
    container_name: backend
    working_dir: /app
    environment:
      - SPRING_PROFILES_ACTIVE=local
      # Connect to shared Neo4j service
      - NEO4J_URI=bolt://stageminder-neo4j-shared:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=stageminder2024
      # Development AWS Configuration
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_REGION=us-east-1
      - S3_BUCKET_NAME=test-bucket
      - SPRING_DEVTOOLS_RESTART_ENABLED=true
      - SPRING_DEVTOOLS_LIVERELOAD_ENABLED=true
    ports:
      - "8080:8080"
      - "35729:35729"  # LiveReload port
    volumes:
      - ./Build/backend:/app
      - maven_cache:/root/.m2
    command: >
      bash -c "
        mvn dependency:go-offline -B &&
        mvn spring-boot:run -Dspring-boot.run.jvmArguments='--enable-preview'
      "
    networks:
      - stageminder-shared-network
    restart: unless-stopped

  # Frontend Service - Development Mode
  frontend:
    build:
      context: ./Build/frontend
      dockerfile: Dockerfile.dev
    container_name: frontend
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8080/api/v1
      - HOSTNAME=0.0.0.0
    ports:
      - "3000:3000"
    volumes:
      # Mount source code for hot updates
      - ./Build/frontend/src:/app/src
      - ./Build/frontend/public:/app/public
      - ./Build/frontend/messages:/app/messages
    depends_on:
      - backend
    networks:
      - stageminder-shared-network
    restart: unless-stopped

  # Nginx Reverse Proxy - Development
  nginx:
    image: nginx:alpine
    container_name: stageminder-nginx
    ports:
      - "80:80"
    depends_on:
      - frontend
      - backend
    networks:
      - stageminder-shared-network
    restart: unless-stopped
    volumes:
      - ./Scripts/Configuration/nginx/nginx-local.conf:/etc/nginx/nginx.conf:ro

volumes:
  maven_cache:
    driver: local
    name: stageminder_maven_cache

networks:
  stageminder-shared-network:
    external: true
    name: stageminder-shared-network
