#!/bin/bash

# This script deploys the frontend and backend server from build artifacts
# Define folder locations
BACKEND_DIR="/home/<USER>/StageMinder/Build/backend/target"
FRONTEND_DIR="/home/<USER>/StageMinder/Build/frontend"
RUN_BACKEND_DIR="/home/<USER>/StageMinder/Run/backend/"
RUN_FRONTEND_DIR="/home/<USER>/StageMinder/Run/frontend/"


# Function to check if a directory exists
check_directory() {
    if [ ! -d "$1" ]; then
        echo "Error: Directory $1 does not exist."
        exit 1
    fi
}

#Clean previously placed runtime artifacts.
clean_run() {
    sudo rm -r $RUN_FRONTEND_DIR/.next
    rm -r $RUN_FRONTEND_DIR/scripts
    rm $RUN_FRONTEND_DIR/.env
    rm -r $RUN_FRONTEND_DIR/src
    rm -r $RUN_BACKEND_DIR/*
}


# Function to run backend
copy_backend() {
    echo "Navigating to backend directory: $BACKEND_DIR"
    cd "$BACKEND_DIR" || { echo "Failed to cd to $BACKEND_DIR"; exit 1; }
    
    # Check if a right folder
    if [ -n "$(find . -maxdepth 1 -type f -name '*.jar')" ]; then
        echo "Copying backend jar to Run..."
        cp *.jar $RUN_BACKEND_DIR
        BACKEND_PID=$!
        echo "Backend copied with PID $BACKEND_PID"
    else
        echo "Error: jar not found in $BACKEND_DIR. Is this a Maven project?"
        exit 1
    fi
    cd ..
    cp -r config $RUN_BACKEND_DIR
    cp -r images $RUN_BACKEND_DIR
    cp -r pdf-files $RUN_BACKEND_DIR
    cp -r json-files $RUN_BACKEND_DIR
    chmod +x *.sh
    cp *.sh $RUN_BACKEND_DIR
    echo "Copied config, images, pdf-files, json-files and .sh files to $RUN_BACKEND_DIR"
}

# Function to run frontend
copy_frontend() {
    echo "Navigating to frontend directory: $FRONTEND_DIR"
    cd "$FRONTEND_DIR" || { echo "Failed to cd to $FRONTEND_DIR"; exit 1; }
    
    # Check if package.json exists (indicating a Node.js/Next.js project)
    if [ -f "package.json" ]; then
        echo "Copying .next from $FRONTEND_DIR to $RUN_FRONTEND_DIR."
        sudo cp -r .next $RUN_FRONTEND_DIR
	cp .env $RUN_FRONTEND_DIR
        cp package.json $RUN_FRONTEND_DIR
        cp next.config.mjs $RUN_FRONTEND_DIR 
        cp -r scripts $RUN_FRONTEND_DIR
        mkdir -p $RUN_FRONTEND_DIR/src
        cp -r src/config $RUN_FRONTEND_DIR/src
        FRONTEND_PID=$!
        echo "Frontend copied to $RUN_FRONTEND_DIR with PID $FRONTEND_PID"
    else
        echo "Error: package.json not found in $FRONTEND_DIR. Is this a Next.js project?"
        exit 1
    fi
}
killall -9 logrotate
# Main execution
echo "Starting StageMinder deployment.."

# Check if directories exist
check_directory "$BACKEND_DIR"
check_directory "$FRONTEND_DIR"

# Build backend and frontend in the background
clean_run
copy_backend
copy_frontend

# Wait for both processes to complete
wait $BACKEND_PID $FRONTEND_PID

echo "StageMinder deployment completed"