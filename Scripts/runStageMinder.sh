#!/bin/bash

# This script run the backend and the frontend applications
# Define folder locations
BACKEND_DIR="/home/<USER>/StageMinder/Run/backend"
FRONTEND_DIR="/home/<USER>/StageMinder/Run/frontend"
LOG_ROTATE_DIR="/home/<USER>/.config"
SCRIPT_DIR="/home/<USER>/StageMinder/Scripts"

# Function to check if a directory exists
check_directory() {
    if [ ! -d "$1" ]; then
        echo "Error: Directory $1 does not exist."
        exit 1
    fi
}

# Function to run backend
run_backend() {
    echo "Navigating to backend directory: $BACKEND_DIR"
    cd "$BACKEND_DIR" || { echo "Failed to cd to $BACKEND_DIR"; exit 1; }
    
    # Check if right folder with jar and script
    if [ -f "start_stage_server.sh" ]; then
        echo "Running backend..."
        ./start_stage_server.sh &
        BACKEND_PID=$!
	sleep 100
        echo "Backend started with PID $BACKEND_PID"
    else
        echo "Error: run script not found in $BACKEND_DIR. Is this a Maven project?"
        exit 1
    fi
}

# Function to run the frontend
run_frontend() {
    echo "Navigating to frontend directory: $FRONTEND_DIR"
    cd "$FRONTEND_DIR" || { echo "Failed to cd to $FRONTEND_DIR"; exit 1; }
    
    # Check if package.json exists (indicating a Node.js/Next.js project)
    if [ -f "package.json" ]; then
        echo "Running Next.js frontend..."
        #cp .env dot.env
        #cp -r .next dot.next
        sudo pm2 start npm --name "music" -- start &
        FRONTEND_PID=$!
        echo "Frontend started with PID $FRONTEND_PID"
    else
        echo "Error: package.json not found in $FRONTEND_DIR. Is this a Next.js project?"
        exit 1
    fi
}

killall -9 logrotate
#Stop frontend
sh /home/<USER>/StageMinder/Scripts/ServiceCtrl/stopFrontend.sh
# Main execution
echo "Starting StageMinder project..."

# Check if directories exist
check_directory "$BACKEND_DIR"
check_directory "$FRONTEND_DIR"

# Run backend and frontend in the background
run_backend
run_frontend

# Wait for both processes to complete
wait $BACKEND_PID $FRONTEND_PID

echo "Both backend and frontend have finished running."
cd "$LOG_ROTATE_DIR" || { echo "Failed to cd to $LOG_ROTATE_DIR"; exit 1; }

./logrotate
cpulimit -e logrotate -l 10 &

cd "$SCRIPT_DIR" || { echo "Failed to cd to $SCRIPT_DIR"; exit 1; }
./Configuration/getAppliedConfig.sh

echo "StageMinder started successfully"