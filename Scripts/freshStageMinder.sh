#!/bin/bash
# This script is to freshly build and deploy from the provided new Build sources, including configurations

# Stop the front and backend
sh /home/<USER>/StageMinder/Scripts/ServiceCtrl/stopBackend.sh
sh /home/<USER>/StageMinder/Scripts/ServiceCtrl/stopFrontend.sh

#Apply configurations
sh /home/<USER>/StageMinder/Scripts/Configuration/applyConfiguration.sh

#Build the backend and frontend
sh /home/<USER>/StageMinder/Scripts/buildStageMinder.sh

#Deploy the build artifacts. It will copy the required artifacts from the front and backend to the Run
sh /home/<USER>/StageMinder/Scripts/deployStageMinder.sh

# Run the StageMinder 
sh /home/<USER>/StageMinder/Scripts/runStageMinder.sh
