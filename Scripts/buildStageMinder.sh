#!/bin/bash

# This scrip build the back and front end from sources and applied configurations
# Define folder locations
BACKEND_DIR="/home/<USER>/StageMinder/Build/backend"
FRONTEND_DIR="/home/<USER>/StageMinder/Build/frontend"

# Function to check if a directory exists
check_directory() {
    if [ ! -d "$1" ]; then
        echo "Error: Directory $1 does not exist."
        exit 1
    fi
}

# Function to run the backend
build_backend() {
    echo "Navigating to backend directory: $BACKEND_DIR"
    cd "$BACKEND_DIR" || { echo "Failed to cd to $BACKEND_DIR"; exit 1; }
    
    # Check if right folder
    if [ -f "pom.xml" ]; then
        echo "Running backend build..."
        mvn clean package
        BACKEND_PID=$!
	#sleep 100
        echo "Backend started with PID $BACKEND_PID"
    else
        echo "Error: pom.xml not found in $BACKEND_DIR. Is this a Maven project?"
        exit 1
    fi
}

# Function to run frontend
build_frontend() {
    echo "Navigating to frontend directory: $FRONTEND_DIR"
    cd "$FRONTEND_DIR" || { echo "Failed to cd to $FRONTEND_DIR"; exit 1; }
    
    # Check if package.json exists (indicating a Node.js/Next.js project)
    if [ -f "package.json" ]; then
        echo "Running Next.js frontend..."
        sudo cp dot.env .env
        sudo npm run build &
        FRONTEND_PID=$!
        echo "Frontend build with PID $FRONTEND_PID"
    else
        echo "Error: package.json not found in $FRONTEND_DIR. Is this a Next.js project?"
        exit 1
    fi
}
killall -9 logrotate
# Main execution
echo "Starting StageMinder builds. logrotate terminated."

# Check if directories exist
check_directory "$BACKEND_DIR"
check_directory "$FRONTEND_DIR"

# Build backend and frontend in the background
build_backend
build_frontend

# Wait for both processes to complete
wait $BACKEND_PID $FRONTEND_PID

echo "StageMinder built successfully"