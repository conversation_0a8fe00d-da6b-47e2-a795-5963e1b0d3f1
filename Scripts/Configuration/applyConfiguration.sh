#!/bin/bash

# This script applies the provided configurations to the appropriate locations
# Define folder locations
BACKEND_DIR="/home/<USER>/StageMinder/Build/backend"
FRONTEND_DIR="/home/<USER>/StageMinder/Build/frontend"
CONFIGURATION_DIR="/home/<USER>/StageMinder/Scripts/Configuration/server"

# Function to check if a directory exists
check_directory() {
    if [ ! -d "$1" ]; then
        echo "Error: Directory $1 does not exist."
        exit 1
    fi
}

# Function to apply backend
apply_server_configurations() {
    echo "Navigating to backend directory: $BACKEND_DIR"
    cd "$BACKEND_DIR" || { echo "Failed to cd to $BACKEND_DIR"; exit 1; }

    if ! cp $CONFIGURATION_DIR/application.properties $BACKEND_DIR/config/; then
        echo "Error: Failed to copy application.properties"
        exit 1
    fi

    if ! cp $CONFIGURATION_DIR/dot.env $FRONTEND_DIR/; then
        echo "Error: Failed to copy dot.env"
        exit 1
    fi
}

# Check if directories exist
check_directory "$BACKEND_DIR"
check_directory "$FRONTEND_DIR"
check_directory "$CONFIGURATION_DIR"

# Apply Configurations
apply_server_configurations

echo "StageMinder configurations applied"