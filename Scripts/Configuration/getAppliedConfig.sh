#!/bin/bash

# Define folder locations
BACKEND_DIR="/home/<USER>/StageMinder/Run/backend"
FRONTEND_DIR="/home/<USER>/StageMinder/Run/frontend"
CONFIGURATION_DIR="/home/<USER>/StageMinder/Scripts/Configuration/applied"

# Function to check if a directory exists
check_directory() {
    if [ ! -d "$1" ]; then
        echo "Error: Directory $1 does not exist."
        exit 1
    fi
}

# Function to apply the backend
get_configurations() {
    if ! cp $BACKEND_DIR/config/application.properties $CONFIGURATION_DIR/; then
        echo "Error: Failed to copy application.properties"
        exit 1
    fi

    if ! cp $FRONTEND_DIR/.env $CONFIGURATION_DIR/dot.env; then
        echo "Error: Failed to copy dot.env"
        exit 1
    fi

    # get the nginx config 
    if ! cp /etc/nginx/sites-available/default $CONFIGURATION_DIR/; then
        echo "Error: Failed to copy nginx default config"
        exit 1
    fi

    if ! cp /etc/neo4j/neo4j.conf $CONFIGURATION_DIR/; then
        echo "Error: Failed to copy neo4j.conf"
        exit 1
    fi

}

# Check if directories exist
check_directory "$BACKEND_DIR"
check_directory "$FRONTEND_DIR"
check_directory "$CONFIGURATION_DIR"

# Get Configurations
get_configurations

echo "StageMinder current configurations retrieved "