# StageMinder Configuration Management Scripts

## Overview
This directory contains two Bash scripts for managing configuration files in the StageMinder application, located within the `Configuration` directory, which has four subdirectories: `neo4j`, `nginx`, `server`, and `applied`.

- **`applyConfiguration.sh`**: Copies configuration files from the `server` directory to the backend and frontend directories.
- **`getAppliedConfig.sh`**: Retrieves current configuration files from the backend, frontend, NGINX, and Neo4j, storing them in the `applied` directory for validation.

The `applied` directory is also updated automatically when the StageMinder server is run using the `runStageMinder.sh` script, located in the parent directory (`/home/<USER>/StageMinder/Scripts/`).

## Directory Structure
The `Configuration` directory (`/home/<USER>/StageMinder/Scripts/Configuration/`) contains:
- `neo4j/`: Stores Neo4j-related configuration files.
- `nginx/`: Stores NGINX-related configuration files.
- `server/`: Stores source configuration files for backend (`application.properties`) and frontend (`dot.env`).
- `applied/`: Stores retrieved configuration files from the backend, frontend, NGINX, and Neo4j, updated by `getAppliedConfig.sh` or `runStageMinder.sh`.

## Prerequisites
- Bash shell environment
- Read and write permissions for the specified directories and files
- Required directories:
  - Backend: `/home/<USER>/StageMinder/Build/backend`
  - Frontend: `/home/<USER>/StageMinder/Build/frontend`
  - Configuration directories:
    - Source: `/home/<USER>/StageMinder/Scripts/Configuration/server`
    - Destination: `/home/<USER>/StageMinder/Scripts/Configuration/applied`
- NGINX and Neo4j services installed and configured (for `getAppliedConfig.sh`)
- `runStageMinder.sh` script in `/home/<USER>/StageMinder/Scripts/` for server runtime configuration updates

## Scripts

### 1. applyConfiguration.sh
This script applies configuration files from the `server` directory to the StageMinder backend and frontend directories.

#### Functionality
1. **Directory Paths**:
   - `BACKEND_DIR`: `/home/<USER>/StageMinder/Build/backend`
   - `FRONTEND_DIR`: `/home/<USER>/StageMinder/Build/frontend`
   - `CONFIGURATION_DIR`: `/home/<USER>/StageMinder/Scripts/Configuration/server`

2. **Directory Validation**:
   - Checks if the backend, frontend, and `server` directories exist.
   - Exits with an error if any directory is missing.

3. **Configuration Application**:
   - Navigates to the backend directory.
   - Copies `application.properties` from `server/` to `BACKEND_DIR/config/`.
   - Copies `dot.env` from `server/` to `FRONTEND_DIR/`.
   - Exits with an error if any copy operation fails.

4. **Success Notification**:
   - Prints `StageMinder configurations applied` upon successful execution.

#### Usage
1. Save the script as `applyConfiguration.sh` in the `Configuration` directory.
2. Make it executable:
   ```bash
   chmod +x applyConfiguration.sh