# Backend server configuration properties

# Database Neo4j related
# URI for connecting to the Neo4j database (default local instance)
spring.neo4j.uri=bolt://localhost:7687
# Username for Neo4j database authentication
spring.neo4j-authentication.usemame=[UserName for DB]
# Password for Neo4j database authentication
spring.neo4j-authentication.password=[Password for DB]
# SSL trust strategy for Neo4j connection (accepts all certificates, less secure)
spring.neo4j.ssl.trust.strategy=TRUST_ALL_CERTIFICATES
# Logging level for Neo4j data operations (set to DEBUG for detailed logs)
org.springframework.data.neo4j=DEBUG

# API related
# Path for API documentation (e.g., Swagger/OpenAPI)
springdoc.api-docs.path=/apidocs
# Active Spring profile (set to production environment)
spring.profiles.active=prod
# Base name for internationalization message files
spring.messages.basename=lang/messages

# Endpoint management
# Exposes management endpoints (e.g., health, metrics) via web
management.endpoints.web.exposure.include=*
# Always show detailed health information for management endpoints
management.endpoint.health.show-details=always
# Enable histogram for HTTP server request metrics
management.metrics.distribution.percentiles-histogram.http.server.requests=true
# Percentiles for HTTP request metrics (50th, 90th, 95th, 99th)
management.metrics.distribution.percentiles.http.server.requests=0.5,0.9,0.95,0.99

# File storage and logging
# Directory for storing JSON configuration files
json.files.location=./json-files
# Directory for storing PDF files
pdf.files.location=/pdf-files
# Root logging level (INFO for standard logging)
logging.level.root=INFO
# Logging level for custom 'kakapo' package (INFO for standard logging)
logging.level.kakapo=INFO

# Password Policy
# Maximum length for user passwords
stage-server.password.max-length=20
# Minimum length for user passwords
stage-server.password.min-length=6
# Minimum number of special characters in passwords (0 means none required)
stage-server.password.min-special-char=0
# Minimum number of uppercase letters in passwords (0 means none required)
stage-server.password.min-uppercase=0

# File upload settings
# Maximum size for a single uploaded file (10MB)
spring.http.multipart.max-file-size=10MB
# Maximum size for the entire multipart request (10MB)
spring.http.multipart.max-request-size=10MB
# Enable multipart file uploads
spring.http.multipart-enabled=true
# Directory for storing uploaded files
spring.http.multipart.location=../uploads
# Maximum file size for servlet multipart handling (10MB, aligns with HTTP settings)
spring.servlet.multipart.max-file-size=10MB
# Maximum request size for servlet multipart handling (10MB, aligns with HTTP settings)
spring.servlet.multipart.max-request-size=10MB

# Email/SMS Tokens
# Expiration time for email/SMS tokens in minutes (15 minutes)
stage-server.narrative=15

# Two Factor Authentication
# Enable two-factor authentication prompt
stage-server-two-factor-authentication-prompt=true
# Length of SMS code for two-factor authentication (6 digits)
stage-server-sms-code-length=6

# Virtual Profile
# Time limit for canceling a virtual profile booking in minutes (30 minutes)
virtual-profile-booking-cancel-time=30

# Server URLs
# URL for the front-end server
stage-server-front-end-url=[URL of the server]
# URL for the back-end server
stage-server-back-end-url=[URL of the server]

# ACT Profile settings
# Supported languages for ACT profile (includes 'Don't Care' option)
stage-server.act.performing-languages=English,French,French(CA),Italian,German,Don't Care

# Email server settings
# SMTP host for sending emails (Gmail)
spring.mail.host=smtp.gmail.com
# SMTP port for email server (587 for TLS)
spring.mail.port=587
# Username for email server authentication
spring.mail.usemame=[Email User Name]
# Password for email server authentication
spring.mail.password=[Email Password]
# Enable SMTP authentication
spring.mail.properties.mail.smtp.auth=true
# Enable STARTTLS for secure email communication
spring.mail.properties.mail.smtp.starttls.enable=true

# JWT settings
# Secret key for signing JSON Web Tokens
app.jwt-secret=[JWT scret]
# JWT token expiration time in milliseconds (30 days)
app.jwt-expiration-milliseconds=108000000

# Google API
# API key for Google services (e.g., Maps)
google.api.key=[Google API Key for map, etc]

# OAuth2 settings for Google
# Google OAuth2 client ID for authentication
spring.security.oauth2.client.registration.google.client-id=[oauth2 Client ID]
# Google OAuth2 client secret
spring.security.oauth2.client.registration.google.client-secret=[oauth2 client scret]
# Google OAuth2 redirect URI for authentication callback
spring.security.oauth2.client.registration.google.redirect-uri=[baseUrl]/login/oauth2/code/{registrationId}

# OAuth2 settings for Facebook
# Facebook OAuth2 redirect URI for authentication callback
spring.security.oauth2.client.registration.facebook.redirect-uri=[baseUrl]/login/oauth2/code/{registrationId}
# Facebook OAuth2 client ID
spring.security.oauth2.client.registration.facebook.client-id=[Facebook client id]
# Facebook OAuth2 client secret
spring.security.oauth2.client.registration.facebook.client-secret=[Facebook scret]
# OAuth2 scopes for Facebook (email and public profile access)
spring.security.oauth2.client.registration.facebook.scopes=email,public_profile

# Stripe Payment settings
# Stripe payment key for processing payments
stripe.payment.key=[Stripe Payment Key]
# Stripe webhook secret for handling payment events
stripe.webhook.key=[Stripe webhook scret]
# Default payment amount for Stripe transactions (1000, likely in cents)
stage-server-stripe-payment-amount=1000
# Currency for Stripe payments (Canadian Dollar)
stage-server-stripe-payment-currency=CAD
# Name for Stripe payment description
stage-server-stripe-payment-name=StageMinder Payment
# Default quantity for Stripe payments (1)
stage-server-stripe-payment-quantity=1

# Server settings
# Enable native handling of forwarded headers (e.g., X-Forwarded-For)
server.forward-headers-strategy=native

# AWS S3 settings
# AWS region for S3 bucket (US East Ohio)
aws.region=us-east-2
# AWS access key for S3 authentication
aws.access-key=[S3 IAM Key]
# AWS secret key for S3 authentication
aws.secret-key=[S3 IAM scret]
# Name of the S3 bucket for file storage
aws.s3.bucket-name=[S3 bucket-name]