# Application Configuration

## Overview
This covers the configuration settings for a Spring Boot-based backend application (`application.properties`) and a Next.js-based frontend application (environment variables). The backend configuration defines properties for database connections, API settings, file uploads, email/SMS services, authentication, payment processing, and cloud storage. The frontend configuration defines environment variables for API and WebSocket connections, as well as Google Maps integration. This document provides an overview, setup instructions, a list of placeholders to replace, and key considerations for deployment.

## Table of Contents
- [Purpose](#purpose)
- [Backend Configuration Sections](#backend-configuration-sections)
- [Frontend Configuration](#frontend-configuration)
- [Placeholders to Replace](#placeholders-to-replace)
- [Setup Instructions](#setup-instructions)
- [Environment Variables](#environment-variables)
- [Important Notes](#important-notes)
- [Contact](#contact)

## Purpose
The `application.properties` file configures the backend server for:
- Connecting to a Neo4j database for data storage.
- Managing API documentation and endpoints.
- Handling file uploads (JSON, PDF) and logging.
- Enforcing password policies and two-factor authentication.
- Sending emails and SMS for notifications and authentication.
- Securing the application with JWT and OAuth2 (Google, Facebook).
- Processing payments via Stripe.
- Storing files in AWS S3.
- Supporting virtual profile and booking features.

The frontend environment variables configure:
- API and WebSocket connections to the backend.
- Integration with Google Maps for location-based features.

## Backend Configuration Sections

### 1. **Neo4j Database**
- **Purpose**: Configures the connection to a Neo4j graph database.
- **Key Properties**:
  - `spring.neo4j.uri`: URI for Neo4j (default: `bolt://localhost:7687`).
  - `spring.neo4j-authentication.username/password`: Credentials for Neo4j access.
  - `spring.neo4j.ssl.trust.strategy`: SSL trust strategy (`TRUST_ALL_CERTIFICATES` for less secure setups).
  - `org.springframework.data.neo4j`: Logging level for Neo4j operations (`DEBUG`).

### 2. **API Settings**
- **Purpose**: Configures API documentation and Spring profiles.
- **Key Properties**:
  - `springdoc.api-docs.path`: Path for API documentation (`/apidocs`).
  - `spring.profiles.active`: Active profile (`prod` for production).
  - `spring.messages.basename`: Base name for internationalization messages (`lang/messages`).

### 3. **Management Endpoints**
- **Purpose**: Exposes management endpoints for monitoring and metrics.
- **Key Properties**:
  - `management.endpoints.web.exposure.include`: Exposes all endpoints (`*`).
  - `management.endpoint.health.show-details`: Shows detailed health info (`always`).
  - `management.metrics.distribution.*`: Configures HTTP request metrics percentiles.

### 4. **File Storage and Logging**
- **Purpose**: Defines locations for JSON/PDF files and logging levels.
- **Key Properties**:
  - `json.files.location`: Directory for JSON files (`/config/json`).
  - `pdf.files.location`: Directory for PDF files (`/pdf-files`).
  - `logging.level.root/kakapo`: Logging levels (`INFO`).

### 5. **Password Policy**
- **Purpose**: Enforces password requirements.
- **Key Properties**:
  - `stage-server.password.max-length/min-length`: Password length (6-20 characters).
  - `stage-server.password.min-special-char/min-uppercase`: No special characters or uppercase letters required (`0`).

### 6. **File Uploads**
- **Purpose**: Configures multipart file uploads.
- **Key Properties**:
  - `spring.http.multipart.max-file-size/max-request-size`: Max file/request size (10MB).
  - `spring.http.multipart-enabled`: Enables multipart uploads (`true`).
  - `spring.http.multipart.location`: Upload directory (`../uploads`).

### 7. **Email/SMS Tokens**
- **Purpose**: Manages token expiration for email/SMS authentication.
- **Key Properties**:
  - `stage-server.token.expiration`: Token expiration time (15 minutes).
  - `stage-server-two-factor-authentication-prompt`: Enables 2FA prompt (`true`).
  - `stage-server-sms-code-length`: SMS code length (6 digits).

### 8. **Virtual Profile**
- **Purpose**: Configures virtual profile booking settings.
- **Key Properties**:
  - `virtual-profile-booking-cancel-time`: Cancellation window (30 minutes).

### 9. **Server URLs**
- **Purpose**: Defines frontend and backend server URLs.
- **Key Properties**:
  - `stage-server-front-end-url/back-end-url`: URLs for frontend and backend servers.

### 10. **ACT Profile**
- **Purpose**: Specifies supported languages for ACT profiles.
- **Key Properties**:
  - `stage-server.act.performing-languages`: Supported languages (English, French, French(CA), Italian, German, Don't Care).

### 11. **Email Server**
- **Purpose**: Configures SMTP for sending emails via Gmail.
- **Key Properties**:
  - `spring.mail.host/port`: SMTP host (`smtp.gmail.com`) and port (`587`).
  - `spring.mail.username/password`: Email credentials.
  - `spring.mail.properties.mail.smtp.auth/starttls.enable`: Enables SMTP authentication and STARTTLS.

### 12. **JWT Settings**
- **Purpose**: Configures JSON Web Tokens for authentication.
- **Key Properties**:
  - `app.jwt-secret`: Secret key for signing JWTs.
  - `app.jwt-expiration-milliseconds`: Token expiration (30 days).

### 13. **Google API**
- **Purpose**: Configures Google API services (e.g., Maps).
- **Key Properties**:
  - `google.api.key`: API key for Google services.

### 14. **OAuth2 (Google and Facebook)**
- **Purpose**: Configures OAuth2 for Google and Facebook authentication.
- **Key Properties**:
  - `spring.security.oauth2.client.registration.google/facebook.*`: Client IDs, secrets, and redirect URIs.
  - `spring.security.oauth2.client.registration.facebook.scopes`: Scopes for Facebook (`email,public_profile`).

### 15. **Stripe Payments**
- **Purpose**: Configures Stripe for payment processing.
- **Key Properties**:
  - `stripe.payment.key/webhook.key`: Stripe API and webhook keys.
  - `stage-server-stripe-payment-amount/currency/name/quantity`: Payment details (1000 CAD, StageMinder Payment, quantity 1).

### 16. **AWS S3**
- **Purpose**: Configures AWS S3 for file storage.
- **Key Properties**:
  - `aws.region`: AWS region (`us-east-2`).
  - `aws.access-key/secret-key`: IAM credentials for S3.
  - `aws.s3.bucket-name`: S3 bucket name.

## Frontend Configuration
The frontend application (built with Next.js) requires the following environment variables, typically defined in a `.env.local` or `.env` file in the frontend project root:

- **Purpose**: Configures API and WebSocket connections and Google Maps integration.
- **Key Properties**:
  - `NEXT_PUBLIC_API_HOST`: Domain name of the frontend server (e.g., `frontend.example.com`).
  - `NEXT_PUBLIC_API_PORT`: Port for the frontend server (e.g., `443` for HTTPS).
  - `NEXT_PUBLIC_API_URL`: Full API URL for backend communication (e.g., `https://${NEXT_PUBLIC_API_HOST}:${NEXT_PUBLIC_API_PORT}/api/v1/`).
  - `NEXT_PUBLIC_SOCKET_URL`: WebSocket URL for real-time communication (e.g., `https://${NEXT_PUBLIC_API_HOST}:${NEXT_PUBLIC_API_PORT}/ws`).
  - `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY`: Google Maps API key for location-based features.

**Example `.env.local`**:
- NEXT_PUBLIC_API_HOST=[Domain name of the frontend]
- NEXT_PUBLIC_API_PORT=[frontend port]
- NEXT_PUBLIC_API_URL=https://${NEXT_PUBLIC_API_HOST}:${NEXT_PUBLIC_API_PORT}/api/v1/
- NEXT_PUBLIC_SOCKET_URL=https://${NEXT_PUBLIC_API_HOST}:${NEXT_PUBLIC_API_PORT}/ws
- NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=[Google API Key]



# Placeholders to Replace

This document lists all placeholders in the backend (`application.properties`) and frontend (`.env.local`) configuration files that must be replaced with actual values before deployment. These placeholders typically involve sensitive data or environment-specific configurations. Store these values securely using environment variables or a secrets management system (e.g., AWS Secrets Manager, Vault) instead of hardcoding them.

## Placeholder List

| **Property** | **Placeholder** | **Description** | **Backend/Frontend** |
|--------------|-----------------|-----------------|----------------------|
| `spring.neo4j-authentication.username` | `[UserName for DB]` | Username for Neo4j database access. | Backend |
| `spring.neo4j-authentication.password` | `[Password for DB]` | Password for Neo4j database access. | Backend |
| `stage-server-front-end-url` | `[URL of the server]` | URL for the frontend server (e.g., `https://frontend.example.com`). | Backend |
| `stage-server-back-end-url` | `[URL of the server]` | URL for the backend server (e.g., `https://backend.example.com`). | Backend |
| `spring.mail.username` | `[Email User Name]` | Gmail account username for SMTP email sending. | Backend |
| `spring.mail.password` | `[Email Password]` | Gmail account password or app-specific password for SMTP. | Backend |
| `app.jwt-secret` | `[JWT scret]` | Secret key for signing JSON Web Tokens (e.g., a secure random string). | Backend |
| `google.api.key` | `[Google API Key for map, etc]` | API key for Google services (e.g., Maps). | Backend |
| `spring.security.oauth2.client.registration.google.client-id` | `[oauth2 Client ID]` | Google OAuth2 client ID for authentication. | Backend |
| `spring.security.oauth2.client.registration.google.client-secret` | `[oauth2 client scret]` | Google OAuth2 client secret. | Backend |
| `spring.security.oauth2.client.registration.google.redirect-uri` | `[baseUrl]/login/oauth2/code/{registrationId}` | Google OAuth2 redirect URI (replace `[baseUrl]` with actual base URL). | Backend |
| `spring.security.oauth2.client.registration.facebook.redirect-uri` | `[baseUrl]/login/oauth2/code/{registrationId}` | Facebook OAuth2 redirect URI (replace `[baseUrl]` with actual base URL). | Backend |
| `spring.security.oauth2.client.registration.facebook.client-id` | `[Facebook client id]` | Facebook OAuth2 client ID. | Backend |
| `spring.security.oauth2.client.registration.facebook.client-secret` | `[Facebook scret]` | Facebook OAuth2 client secret. | Backend |
| `stripe.payment.key` | `[Stripe Payment Key]` | Stripe API key for payment processing. | Backend |
| `stripe.webhook.key` | `[Stripe webhook scret]` | Stripe webhook secret for handling payment events. | Backend |
| `aws.access-key` | `[S3 IAM Key]` | AWS IAM access key for S3 authentication. | Backend |
| `aws.secret-key` | `[S3 IAM scret]` | AWS IAM secret key for S3 authentication. | Backend |
| `aws.s3.bucket-name` | `[S3 bucket-name]` | Name of the AWS S3 bucket for file storage. | Backend |
| `NEXT_PUBLIC_API_HOST` | `[Domain name of the frontend]` | Domain name of the frontend server (e.g., `frontend.example.com`). | Frontend |
| `NEXT_PUBLIC_API_PORT` | `[frontend port]` | Port for the frontend server (e.g., `443` for HTTPS). | Frontend |
| `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY` | `[Google API Key]` | Google Maps API key for frontend location-based features. | Frontend |

## Notes
- **Obtaining Values**:
  - **Neo4j Credentials**: Set up a Neo4j database and create a user with appropriate permissions.
  - **Server URLs**: Determine the deployed URLs for your frontend and backend servers.
  - **Gmail Credentials**: Use a Gmail account with an app-specific password for SMTP.
  - **JWT Secret**: Generate a secure random string (e.g., using a tool like `openssl rand -base64 32`).
  - **Google API Keys**: Obtain from the Google Cloud Console (ensure Maps API is enabled for frontend and backend).
  - **OAuth2 Credentials**: Create OAuth2 credentials in Google Cloud Console and Facebook Developer Portal.
  - **Stripe Keys**: Obtain from the Stripe Dashboard (use test keys for development).
  - **AWS S3 Credentials**: Create an IAM user with S3 access and generate access/secret keys in AWS Console.
  - **S3 Bucket Name**: Create a bucket in AWS S3 and note its name.
- **Security**: Avoid hardcoding sensitive values. Use environment variables or a secrets management system.
- **Google API Key Reuse**: The backend (`google.api.key`) and frontend (`NEXT_PUBLIC_GOOGLE_MAPS_API_KEY`) may use the same Google API key if they share the same scope (e.g., Maps API).

For setup instructions, refer to the main [README.md](README.md).

---
*Generated on May 25, 2025*
